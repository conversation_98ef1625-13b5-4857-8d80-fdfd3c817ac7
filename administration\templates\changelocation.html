<div class="row page" data-ng-controller="ChangeLocation">
    <div class="col-md-12">
        <article class="article">

            <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled" md-whiteframe="4" md-disable-close-events>

                <md-toolbar class="md-theme-default">
                    <h1 class="md-toolbar-tools">Custom Pallet Matching Details</h1>
                    <i class="material-icons" style=" position: absolute; top: 10px; right:10px;" ng-click="toggleSidenav()">close</i>
                </md-toolbar>


                <div>

                    <form name="LoadVerification" class="form-validation mt-10">
                        <div class="col-md-10 col-md-offset-1">

                            <p class="col-md-6">

                                <span  ng-if="cpallet.CustomPalletDistinctValues.MPN > 1">
                                    <md-icon class="material-icons notverified_field mr-5">close</md-icon>
                                </span>

                                <span  ng-if="cpallet.CustomPalletDistinctValues.MPN < 2">
                                    <md-icon class="material-icons verified_field mr-5">check</md-icon>
                                </span>


                               <!--  <span>
                                    <md-icon class="material-icons text-danger mr-5">close</md-icon>
                                </span> -->

                                <span>MPN</span>
                            </p>

                            <p class="col-md-6">

                                <span ng-if="cpallet.CustomPalletDistinctValues.Class > 1">
                                    <md-icon class="material-icons notverified_field mr-5">close</md-icon>
                                </span>

                                <span  ng-if="cpallet.CustomPalletDistinctValues.Class < 2">
                                    <md-icon class="material-icons verified_field mr-5">check</md-icon>
                                </span>


                                <!-- <span>
                                    <md-icon class="material-icons text-success mr-5">check</md-icon>
                                </span> -->

                                <span>Class</span>
                            </p>

                            <p class="col-md-6">

                                <span ng-if="cpallet.CustomPalletDistinctValues.Category > 1">
                                    <md-icon class="material-icons notverified_field mr-5">close</md-icon>
                                </span>

                                <span  ng-if="cpallet.CustomPalletDistinctValues.Category < 2">
                                    <md-icon class="material-icons verified_field mr-5">check</md-icon>
                                </span>


                                <!-- <span>
                                    <md-icon class="material-icons text-success mr-5">check</md-icon>
                                </span> -->

                                <span>Category</span>
                            </p>

                            <p class="col-md-6">

                                <span ng-if="cpallet.CustomPalletDistinctValues.Disposition > 1">
                                    <md-icon class="material-icons notverified_field mr-5">close</md-icon>
                                </span>

                                <span ng-if="cpallet.CustomPalletDistinctValues.Disposition < 2">
                                    <md-icon class="material-icons verified_field mr-5">check</md-icon>
                                </span>


                               <!--  <span>
                                    <md-icon class="material-icons text-danger mr-5">close</md-icon>
                                </span> -->

                                <span>Disposition</span>
                            </p>

                            <div class="col-md-12 btns-row">
                                <md-button class="md-button md-raised btn-w-md md-default" ng-click="toggleSidenav()">Close</md-button>
                            </div>



                        </div>
                    </form>

                </div>

            </md-sidenav>



            <md-card class="no-margin-h">
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Change Location</span>
                    </div>
                </md-toolbar>
                <div class="row">
                    <form>
                        <div class="col-md-12">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Scan Inbound Container</label>
                                    <input type="text" name="PalletID" ng-model="changelocation.PalletID" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="PalletID" ng-disabled="!changelocation.PalletID" ng-click="GetPalletDetails()">
                                        <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                    </md-button>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block includedsearch">
                                    <label>Scan Bin </label>
                                    <input type="text" name="CpalletID" ng-model="changelocation.CpalletID" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" type="button" aria-label="CpalletID" ng-disabled="!changelocation.CpalletID" ng-click="GetCPalletDetails()">
                                        <md-icon md-svg-src="../assets/images/search.svg" ></md-icon>
                                    </md-button>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3" style="display: none;">
                                <md-input-container class="md-block">
                                    <label>Scan Outbound Container</label>
                                    <input type="text" name="ShippingContainerID" ng-model="changelocation.ShippingContainerID" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="ShippingContainerID" ng-disabled="!changelocation.ShippingContainerID" ng-click="GetShippingContainerDetails()">
                                        <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                    </md-button>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>
                        </div>
                    </form>
                </div>

            </md-card>


            <!--Pallet ID list start-->
            <md-card class="no-margin-h pt-0 mt-0" ng-if="palletdetails.length >0">
            <!-- <md-card class="no-margin-h pt-0 mt-0"> -->
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Inbound Container Selected</span>
                    </div>
                </md-toolbar>
                <div class="row">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div class="table-container table-responsive" style="overflow: auto;">

                                <table class="table mb-0">
                                    <thead>
                                        <tr>
                                            <th style="min-width:60px;"> Action</th>
                                            <th style="min-width:120px;">Inbound Container ID</th>
                                            <th style="min-width:130px;">Facility</th>
                                            <th style="min-width:130px;">Location</th>
                                            <th style="min-width:150px;">Location Group</th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        <tr ng-repeat="pallet in palletdetails" id="{{pallet.idPallet}}">
                                       <!--  <tr> -->
                                            <td class="actionicons" style="min-width:60px;"><a href="../label/master/examples/palletlabel.php?id={{pallet.idPallet}}" target="new"><i class="material-icons print">print</i></a></td>
                                            <td>{{pallet.idPallet}}</td>
                                            <td>{{pallet.FacilityName}}</td>
                                            <td>{{pallet.LocationName}}</td>
                                           <td>

                                                <!--<md-input-container md-no-float class="md-block tdinput" style="width:70%; float:left;">
                                                    <input type="text" name="GroupID" ng-model="pallet.GroupID" list="palletgroup{{$index}}" class="form-control" autocomplete="off" placeholder="Location Group" >
                                                    <datalist id="palletgroup{{$index}}">
                                                        <option value="{{group.GroupName}}" ng-repeat="group in pallet.LocationGroups">{{group.GroupName}} ({{group.available}})  / {{group.total}} ({{group.LocationType}})</option>
                                                    </datalist>
                                                </md-input-container>
                                                <md-button class="md-fab md-raised md-accent md-mini" type="button" ng-disabled="!pallet.GroupID" ng-click="showPalletconfirm(pallet.idPallet,'pallettype','',pallet,'Group')" >
                                                    <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                </md-button>-->
                                                <div class="autocomplete insideuse">
                                                    <md-autocomplete required style="width: 180px;"
                                                        md-no-cache="noCache"
                                                        md-search-text-change="ContainerLocationChange1(pallet.GroupID,pallet)"
                                                        md-search-text="pallet.GroupID"
                                                        md-items="item in queryContainerLocationSearch1(pallet.GroupID,pallet)"
                                                        md-item-text="item.GroupName"
                                                        md-selected-item-change="selectedContainerLocationChange1(item,pallet)"
                                                        md-min-length="0"
                                                        ng-model-options='{ debounce: 1000 }'
                                                        placeholder="Search Location Group">
                                                        <md-item-template>
                                                            <span md-highlight-text="pallet.GroupID" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                        </md-item-template>
                                                        <md-not-found>
                                                            No Records matching "{{pallet.GroupID}}" were found.
                                                        </md-not-found>
                                                    </md-autocomplete>
                                                    <button  style="min-width: 40px; min-height: 30px; margin-top: -6px;" class="md-button md-raised md-accent"  type="button" ng-disabled="!pallet.GroupID" ng-click="showPalletconfirm(pallet.idPallet,'pallettype',pallet.GroupID,pallet,'Group')">
                                                        <!-- Update -->
                                                        <span ng-show="! pallet.busy">Update</span>
                                                        <span ng-show="pallet.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                             </table>

                            </div>
                        </div>
                    </div>
                </div>

            </md-card>
            <!--Pallet ID list Close-->

            <!--Shipping container list start-->
            <md-card class="no-margin-h pt-0 mt-0" ng-if="shippingcontainerdetails.length >0">
            <!-- <md-card class="no-margin-h pt-0 mt-0"> -->
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Outbound Container Selected</span>
                    </div>
                </md-toolbar>
                <div class="row">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div class="table-container table-responsive" style="overflow: auto;">

                                <table class="table mb-0">
                                    <thead>
                                        <tr>
                                            <th style="min-width:60px;"> Action</th>
                                            <th style="min-width:120px;">OB Container ID</th>
                                            <th style="min-width:130px;">Facility</th>
                                            <th style="min-width:130px;">Location</th>
                                            <th style="min-width:150px;">Location Group</th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        <tr ng-repeat="ShippingContainer in shippingcontainerdetails" id="{{ShippingContainer.ShippingContainerID}}">
                                       <!--  <tr> -->
                                            <td class="actionicons" style="min-width:60px;"><a href="../label/master/examples/ShipmentContainerlabel.php?id={{ShippingContainer.ShippingContainerID}}" target="new"><i class="material-icons print">print</i></a></td>
                                            <td>{{ShippingContainer.ShippingContainerID}}</td>
                                            <td>{{ShippingContainer.FacilityName}}</td>
                                            <td>{{ShippingContainer.LocationName}}</td>
                                           <td>

                                                <!--<md-input-container md-no-float class="md-block tdinput" style="width:70%; float:left;">
                                                    <input type="text" name="GroupID" ng-model="ShippingContainer.GroupID" list="ShippingContainerGroup{{$index}}" class="form-control" autocomplete="off" placeholder="Location Group" >
                                                    <datalist id="ShippingContainerGroup{{$index}}">
                                                        <option value="{{group.GroupName}}" ng-repeat="group in ShippingContainer.LocationGroups">{{group.GroupName}} ({{group.available}})  / {{group.total}} ({{group.LocationType}})</option>
                                                    </datalist>
                                                </md-input-container>

                                                <md-button class="md-fab md-raised md-accent md-mini" type="button" ng-disabled="!ShippingContainer.GroupID" ng-click="showPalletconfirm(ShippingContainer.ShippingContainerID,'ShippingContainer','',ShippingContainer,'Group')" >
                                                    <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                </md-button>-->
                                                <div class="autocomplete insideuse">
                                                    <md-autocomplete required style="width: 180px;"
                                                        md-no-cache="noCache"
                                                        md-search-text-change="ContainerLocationChange1(ShippingContainer.GroupID,ShippingContainer)"
                                                        md-search-text="ShippingContainer.GroupID"
                                                        md-items="item in queryContainerLocationSearch1(ShippingContainer.GroupID,ShippingContainer)"
                                                        md-item-text="item.GroupName"
                                                        md-selected-item-change="selectedContainerLocationChange1(item,ShippingContainer)"
                                                        md-min-length="0"
                                                        ng-model-options='{ debounce: 1000 }'
                                                        placeholder="Search Location Group">
                                                        <md-item-template>
                                                            <span md-highlight-text="ShippingContainer.GroupID" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                        </md-item-template>
                                                        <md-not-found>
                                                            No Records matching "{{ShippingContainer.GroupID}}" were found.
                                                        </md-not-found>
                                                    </md-autocomplete>
                                                    <button  style="min-width: 40px; min-height: 30px; margin-top: -6px;" class="md-button md-raised md-accent"  type="button" ng-disabled="!ShippingContainer.GroupID" ng-click="showPalletconfirm(ShippingContainer.ShippingContainerID,'ShippingContainer','',ShippingContainer,'Group')">
                                                        <!-- Update -->
                                                        <span ng-show="! ShippingContainer.busy">Update</span>
                                                        <span ng-show="ShippingContainer.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                             </table>

                            </div>
                        </div>
                    </div>
                </div>

            </md-card>
            <!--Shipping container list Close-->

             <!--Custom Pallet ID list start-->
            <md-card class="no-margin-h pt-0 mt-0" ng-if="cpalletdetails.length >0">
          <!--   <md-card class="no-margin-h pt-0 mt-0"> -->
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Bin Selected</span>
                    </div>
                </md-toolbar>
                <div class="row">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div class="table-container table-responsive" style="overflow: auto;">

                                <table class="table mb-0">
                                    <thead>
                                        <tr>
                                            <th style="min-width:60px;">Action</th>
                                            <th style="min-width:160px;">Bin ID</th>
                                            <th style="min-width:130px;">Facility</th>
                                            <th style="min-width:130px;">Location</th>
                                            <th style="min-width:150px;">Location Group</th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        <tr ng-repeat="cpallet in cpalletdetails">
                                      <!--   <tr> -->
                                            <td class="actionicons" style="min-width:80px;">
                                                <a href="../label/master/examples/cpalletlabel.php?id={{cpallet.CustomPalletID}}" target="new"><i class="material-icons print">print</i></a>
                                                <!--<i class="material-icons open text-success" ng-click="toggleSidenav(cpallet)">open_in_new</i>-->
                                            </td>
                                            <td>{{cpallet.BinName}}</td>
                                            <!-- <td>{{cpallet.Weight}}</td> -->
                                            <td>{{cpallet.FacilityName}}</td>
                                            <td>{{cpallet.LocationName}}</td>
                                            <td>
                                                <!--<md-input-container md-no-float class="md-block tdinput" style="width:70%; float:left;">
                                                    <input type="text" name="GroupID" ng-model="cpallet.GroupID" list="cpalletgroup{{$index}}" class="form-control" autocomplete="off" placeholder="Location Group" >
                                                    <datalist id="cpalletgroup{{$index}}">
                                                        <option value="{{group.GroupName}}" ng-repeat="group in cpallet.LocationGroups">{{group.GroupName}} {{group.available}}  / {{group.total}}  ({{group.LocationType}})</option>
                                                    </datalist>
                                                </md-input-container>
                                                <md-button class="md-fab md-raised md-accent md-mini" type="button" ng-disabled="!cpallet.GroupID"  ng-click="showPalletconfirm(cpallet.CustomPalletID,'custompallettype',cpallet.GroupID,cpallet,'Group')" >
                                                    <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                </md-button>-->
                                                <div class="autocomplete insideuse">
                                                    <md-autocomplete required style="width: 180px;"
                                                        md-no-cache="noCache"
                                                        md-search-text-change="ContainerLocationChange1(cpallet.GroupID,cpallet)"
                                                        md-search-text="cpallet.GroupID"
                                                        md-items="item in queryContainerLocationSearch1(cpallet.GroupID,cpallet)"
                                                        md-item-text="item.GroupName"
                                                        md-selected-item-change="selectedContainerLocationChange1(item,cpallet)"
                                                        md-min-length="0"
                                                        ng-model-options='{ debounce: 1000 }'
                                                        placeholder="Search Location Group">
                                                        <md-item-template>
                                                            <span md-highlight-text="cpallet.GroupID" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                        </md-item-template>
                                                        <md-not-found>
                                                            No Records matching "{{cpallet.GroupID}}" were found.
                                                        </md-not-found>
                                                    </md-autocomplete>
                                                    <button  style="min-width: 40px; min-height: 30px; margin-top: -6px;" class="md-button md-raised md-accent"  type="button" ng-disabled="!cpallet.GroupID" ng-click="showPalletconfirm(cpallet.CustomPalletID,'custompallettype',cpallet.GroupID,cpallet,'Group')">
                                                        <!-- Update -->
                                                        <span ng-show="! cpallet.busy">Update</span>
                                                        <span ng-show="cpallet.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                    </button>
                                                </div>
                                            </td>
                                            <!--<td>
                                                <md-input-container md-no-float class="md-block tdinput" style="width:70%; float:left;">
                                                    <input type="text" name="assetlocationinput" ng-model="cpallet.assetlocationinput" list="cpallet{{$index}}" class="form-control" autocomplete="off" placeholder="Location" >
                                                    <datalist id="cpallet{{$index}}">
                                                        <option value="{{location.LocationName}}" ng-repeat="location in cpallet.location | filter:{ LocationStatus : '!2'} ">{{location.lable_name}} ({{location.LocationType}})</option>
                                                    </datalist>
                                                </md-input-container>

                                                <md-button class="md-fab md-raised md-accent md-mini" type="button"  ng-click="showPalletconfirm(cpallet.CustomPalletID,'custompallettype',cpallet.assetlocationinput,cpallet,'Scan')" >
                                                    <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                </md-button>
                                            </td>-->

                                        </tr>
                                    </tbody>

                                </table>

                            </div>
                        </div>
                    </div>
                </div>

            </md-card>
            <!--Custom Pallet ID list Close-->



         </article>
    </div>

</div>
