<div class="row page" data-ng-controller="parkinglocationConfiguration">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Parking Location Configuration</span>
                        <div flex></div>
                        <div class="upload-btn-wrapper text-center mt-5">
                            <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                            <input type="file" ng-file-select="onFileSelect($files)" id="ParkingLocationFile">  
                            <a href="../../sample_files/upload_ParkingLocation_File.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                        </div> 

                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="parkinglocation_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityID" ng-model="parkinglocation.FacilityID" required ng-disabled="true">
                                        <md-option ng-repeat="facility in Facility | filter:{ FacilityStatus : '!InActive'}" value="{{facility.FacilityID}}"> {{facility.FacilityName}} </md-option>                             
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="parkinglocation_form.FacilityID.$error" multiple ng-if='parkinglocation_form.FacilityID.$dirty'>                            
                                            <div ng-message="required">This is required.</div>                                            
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Location Name</label>
                                    <input type="text" name="ParkingLocationName"  ng-model="parkinglocation['ParkingLocationName']"  required ng-maxlength="45" />
                                    <div class="error-space">
                                    <div ng-messages="parkinglocation_form.ParkingLocationName.$error" multiple ng-if='parkinglocation_form.ParkingLocationName.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input name="Description" ng-model="parkinglocation.Description" required ng-maxlength="250">  
                                    <div class="error-sapce">
                                        <div ng-messages="parkinglocation_form.Description.$error" multiple ng-if='parkinglocation_form.Description.$dirty'> 
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 250.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Type</label>
                                    <md-select name="ParkTypeID" ng-model="parkinglocation.ParkTypeID" required>
                                        <md-option ng-repeat="ParkType in ParkTypes" value="{{ParkType.ParkTypeID}}"> {{ParkType.ParkTypeName}} </md-option>                             
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="parkinglocation_form.ParkTypeID.$error" multiple ng-if='parkinglocation_form.ParkTypeID.$dirty'>                            
                                            <div ng-message="required">This is required.</div>                                            
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="parkinglocation.Status" required aria-label="select">
                                        <md-option value="Active"> Active </md-option>
                                        <md-option value="Inactive"> Inactive </md-option>
                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="parkinglocation_form.Status.$error" multiple ng-if='parkinglocation_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/ParkingLocation" style="text-decoration: none;">
                                    <md-button class="md-button md-raised btn-w-md  md-default">
                                        Cancel
                                    </md-button>
                                </a>

                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="parkinglocation_form.$invalid || parkinglocation.busy" ng-click="SaveParkingLocationConfiguration()">
                                <span ng-show="! parkinglocation.busy">Save</span>
                                <span ng-show="parkinglocation.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>

            <md-card class="no-margin-h pt-0">
                <md-toolbar class="md-table-toolbar md-default" ng-init="ParkingLocationNameConfigurationList = true;">
                    <div class="md-toolbar-tools" style="cursor: pointer;">

                        <i ng-click="ParkingLocationNameConfigurationList = !ParkingLocationNameConfigurationList" class="material-icons md-primary" ng-show="ParkingLocationNameConfigurationList">keyboard_arrow_up</i>
                        <i ng-click="ParkingLocationNameConfigurationList = !ParkingLocationNameConfigurationList" class="material-icons md-primary" ng-show="! ParkingLocationNameConfigurationList">keyboard_arrow_down</i>
                        <span ng-click="ParkingLocationNameConfigurationList = !ParkingLocationNameConfigurationList">Parking Types List</span>
                        <div flex></div>
                        <!-- <a href="#!/SortConfiguration" ng-click="sortconfigurationListxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex;">
                            <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                        </a> -->
                    </div>
                </md-toolbar>

                <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                    <p>No Parking Location Configuration Available </p>
                </div>

                <div class="row"  ng-show="ParkingLocationNameConfigurationList">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div ng-show="pagedItems" class="pull-right mt-10">
                                    <small>
                                    Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                    to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                        <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                    of <span style="font-weight:bold;">{{total}}</span>
                                    </small>
                            </div>
                            <div style="clear:both;"></div>
                            <div class="table-responsive" style="overflow: auto;">
                                <table class="table table-striped mb-0">
                                    <thead>

                                        <tr class="th_sorting">
                                            <th style="min-width: 40px;">Edit</th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">
                                                <div style="min-width: 80px;">
                                                    Facility<i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>
                                                    <span ng-show="OrderBy == 'FacilityName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('ParkingLocationName')" ng-class="{'orderby' : OrderBy == 'ParkingLocationName'}">
                                                <div style="min-width: 80px;">
                                                    Parking Location Name<i class="fa fa-sort pull-right" ng-show="OrderBy != 'ParkingLocationName'"></i>
                                                    <span ng-show="OrderBy == 'ParkingLocationName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>

                                             <th style="cursor:pointer;" ng-click="MakeOrderBy('Description')" ng-class="{'orderby' : OrderBy == 'Description'}">
                                                <div style="min-width: 130px;">
                                                    Description<i class="fa fa-sort pull-right" ng-show="OrderBy != 'Description'"></i>
                                                    <span ng-show="OrderBy == 'Description'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                             <th style="cursor:pointer;" ng-click="MakeOrderBy('ParkTypeName')" ng-class="{'orderby' : OrderBy == 'ParkTypeName'}">
                                                <div style="min-width: 130px;">
                                                    Parking Type<i class="fa fa-sort pull-right" ng-show="OrderBy != 'ParkTypeName'"></i>
                                                    <span ng-show="OrderBy == 'ParkTypeName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">
                                                <div style="min-width: 100px;">
                                                    Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                    <span ng-show="OrderBy == 'Status'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                        </tr>

                                        <tr class="errornone">
                                            <td>&nbsp;</td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="ParkingLocationName" ng-model="filter_text[0].ParkingLocationName" ng-change="MakeFilter()" aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="Description" ng-model="filter_text[0].Description" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="ParkTypeName" ng-model="filter_text[0].ParkTypeName" ng-change="MakeFilter()" aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                </md-input-container>
                                            </td>
                                        </tr>
                                    </thead>

                                    <tbody ng-show="pagedItems.length > 0">
                                        <tr ng-repeat="product in pagedItems">
                                            <td class="action-icons" style="min-width: 40px;">
                                                <span ng-click="EditParkingLocationConfiguration(product,$event)"><i class="material-icons text-danger edit">edit</i></span>
                                            </td>
                                            <td>
                                                {{product.FacilityName}}
                                            </td>
                                            <td>
                                                {{product.ParkingLocationName}}
                                            </td>
                                            <td>
                                                {{product.Description}}
                                            </td>
                                            <td>
                                                {{product.ParkTypeName}}
                                            </td>
                                            <td>
                                                {{product.Status}}
                                            </td>
                                        </tr>
                                    </tbody>

                                    <tfoot>
                                        <tr>
                                            <td colspan="6">
                                                <div>
                                                    <ul class="pagination">
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="firstPage()"><< First</a>
                                                        </li>
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="prevPage()"><< Prev</a>
                                                        </li>
                                                        <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                            <a style="cursor:pointer;">{{n+1}}</a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="nextPage()">Next >></a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="lastPage()">Last >></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    </tfoot>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </md-card>
        </article>
    </div>
</div>
