ALTER TABLE `awsdev`.`fleet_risk_serials`
ADD COLUMN `internal_use` VARCHAR(20) NULL DEFAULT NULL AFTER `special_handling`;

INSERT INTO `awstrn`.`business_rule_attributes` (`attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('Internal Use', 'Internal', 'Internal Use', 'internal_use', 'Active');

INSERT INTO `awstrn`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('26', 'Yes', 'Active');
INSERT INTO `awstrn`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('26', 'No', 'Active');

INSERT INTO `awsdev`.`all_pages` (`TabID`, `TabName`, `TabLink`, `Status`, `GroupName`, `Order`) VALUES ('2', 'Warranty Serials', 'WarrantySerialList', '1', 'eViridis Administration', '60');

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Warranty Serials', 'WarrantySerialList', '', '1', '', '1', 'eViridis Administration', '0', '61', '0');

ALTER TABLE `awsdev`.`asset_rma_investigation`
ADD COLUMN `in_warranty` VARCHAR(5) NULL DEFAULT NULL AFTER `ManufacturerSerialNumber`,
ADD COLUMN `rack_asset_id` VARCHAR(100) NULL DEFAULT NULL AFTER `in_warranty`,
ADD COLUMN `host_id` VARCHAR(100) NULL DEFAULT NULL AFTER `rack_asset_id`,
ADD COLUMN `host_asset_id` VARCHAR(100) NULL DEFAULT NULL AFTER `host_id`;
ALTER TABLE `awstrn`.`workflow_input`
CHANGE COLUMN `input_type` `input_type` VARCHAR(100) NULL DEFAULT NULL ;



After Release
--------------
ALTER TABLE `awsdev`.`asset_sanitization`
ADD COLUMN `nd_sanitization_type` VARCHAR(100) NULL DEFAULT NULL AFTER `SiteID`;


INSERT INTO `awsdev`.`all_pages` (`TabID`, `TabName`, `TabLink`, `Status`, `GroupName`, `Order`) VALUES ('2', 'Disposition Override', 'DispositionOverride', '1', 'eViridis Administration', '61');

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Disposition Override', 'DispositionOverride', '', '', '1', '', '1', 'eViridis Administration', '0', '62', '0');

ALTER TABLE `awsdev`.`asn_assets`
ADD COLUMN `part_type` VARCHAR(100) NULL DEFAULT NULL AFTER `apn_id`;

ALTER TABLE `awsdev`.`asset`
ADD COLUMN `part_type` VARCHAR(100) NULL DEFAULT NULL AFTER `apn_id`;


INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('160', 'Onsite Backlog Report', 'Onsitebacklog', '', '', '1', '', '1', '', '0', '3', '0');

After version2.0 release
-------------------------
ALTER TABLE `awsdev`.`asset`
ADD COLUMN `FirstReceivedCustomPalletID` INT NULL DEFAULT NULL AFTER `part_type`,
ADD COLUMN `FirstReceivedDateTime` DATETIME NULL DEFAULT NULL AFTER `FirstReceivedCustomPalletID`,
ADD COLUMN `FirstReceivedBy` INT NULL DEFAULT NULL AFTER `FirstReceivedDateTime`;


ALTER TABLE `awsdev`.`asset`
ADD COLUMN `FirstSanitizationCustomPalletID` INT NULL DEFAULT NULL AFTER `FirstReceivedBy`,
ADD COLUMN `FirstSanitizationDateTime` DATETIME NULL DEFAULT NULL AFTER `FirstSanitizationCustomPalletID`,
ADD COLUMN `FirstSanitizationBy` INT NULL DEFAULT NULL AFTER `FirstSanitizationDateTime`;


ALTER TABLE `awsdev`.`asset`
ADD COLUMN `FirstFailureAnalysisCustomPalletID` INT NULL DEFAULT NULL AFTER `FirstReceivedBy`,
ADD COLUMN `FirstFailureAnalysisDateTime` DATETIME NULL DEFAULT NULL AFTER `FirstSanitizationCustomPalletID`,
ADD COLUMN `FirstFailureAnalysisBy` INT NULL DEFAULT NULL AFTER `FirstSanitizationDateTime`;

ALTER TABLE `awsdev`.`asset`
ADD COLUMN `FirstRMACustomPalletID` INT NULL DEFAULT NULL AFTER `FirstReceivedBy`,
ADD COLUMN `FirstRMADateTime` DATETIME NULL DEFAULT NULL AFTER `FirstSanitizationCustomPalletID`,
ADD COLUMN `FirstRMABy` INT NULL DEFAULT NULL AFTER `FirstSanitizationDateTime`;

ALTER TABLE `awsdev`.`asset`
ADD COLUMN `FirstHarvestCustomPalletID` INT NULL DEFAULT NULL AFTER `FirstReceivedBy`,
ADD COLUMN `FirstHarvestDateTime` DATETIME NULL DEFAULT NULL AFTER `FirstSanitizationCustomPalletID`,
ADD COLUMN `FirstHarvestBy` INT NULL DEFAULT NULL AFTER `FirstSanitizationDateTime`;


ALTER TABLE `awsdev`.`asset`
ADD COLUMN `FirstRepairCustomPalletID` INT NULL DEFAULT NULL AFTER `FirstReceivedBy`,
ADD COLUMN `FirstRepairDateTime` DATETIME NULL DEFAULT NULL AFTER `FirstSanitizationCustomPalletID`,
ADD COLUMN `FirstRepairBy` INT NULL DEFAULT NULL AFTER `FirstSanitizationDateTime`;


ALTER TABLE `awsdev`.`pallets`
ADD COLUMN `idCustomer` INT NULL DEFAULT NULL AFTER `POF`;

ALTER TABLE `awsstg`.`pallets`
ADD INDEX `pallettocustomer_idx` (`idCustomer` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`pallets`
ADD CONSTRAINT `pallettocustomer`
  FOREIGN KEY (`idCustomer`)
  REFERENCES `awsstg`.`customer` (`CustomerID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `awsdev`.`asset`
ADD COLUMN `DispositionOverrideReason` TEXT NULL DEFAULT NULL AFTER `FirstSanitizationBy`;


ALTER TABLE `awsdev`.`asset`
ADD COLUMN `valid_identity_flag` VARCHAR(5) NULL DEFAULT NULL AFTER `DispositionOverrideReason`,
ADD COLUMN `valid_container_flag` VARCHAR(5) NULL DEFAULT NULL AFTER `valid_identity_flag`,
ADD COLUMN `valid_ticket_flag` VARCHAR(5) NULL DEFAULT NULL AFTER `valid_container_flag`,
ADD COLUMN `valid_seal_flag` VARCHAR(5) NULL DEFAULT NULL AFTER `valid_ticket_flag`,
ADD COLUMN `clean_receive_flag` VARCHAR(5) NULL DEFAULT NULL AFTER `valid_seal_flag`;




CREATE TABLE `awsdev`.`workflow_disposition` (
  `map_id` INT NOT NULL AUTO_INCREMENT,
  `workflow_id` INT NULL DEFAULT NULL,
  `disposition_id` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`map_id`));


CREATE TABLE `awsdev`.`disposition_override_assets` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `AssetScanID` BIGINT NULL DEFAULT NULL,
  `SerialNumber` VARCHAR(100) NULL DEFAULT NULL,
  `FromCustomPalletID` INT NULL DEFAULT NULL,
  `ToCustomPalletID` INT NULL DEFAULT NULL,
  `FromDispositionID` INT NULL DEFAULT NULL,
  `ToDispositionID` INT NULL DEFAULT NULL,
  `OverrideReason` TEXT NULL DEFAULT NULL,
  `Comments` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ID`));

  ALTER TABLE `awsprod`.`disposition_override_assets`
ADD COLUMN `RequesterLogin` VARCHAR(100) NULL DEFAULT NULL AFTER `CreatedBy`;



CREATE TABLE `awsdev`.`source_bin_user_mapping` (
  `map_id` INT NOT NULL AUTO_INCREMENT,
  `CustomPalletID` INT NULL DEFAULT NULL,
  `workflow_id` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`map_id`));

  INSERT INTO `awsstg`.`all_pages` (`TabID`, `TabName`, `TabLink`, `Status`, `GroupName`, `Order`) VALUES ('2', 'Locked Source Bins', 'LockedSourceBins', '1', 'eViridis Administration', '62');
INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Locked Source Bins', 'LockedSourceBins', '', '', '1', '1', 'eViridis Administration', '0', '63', '0');

ALTER TABLE `awsdev`.`disposition`
ADD COLUMN `color_code` VARCHAR(10) NULL DEFAULT NULL AFTER `sub_disposition`,
ADD COLUMN `color` VARCHAR(100) NULL DEFAULT NULL AFTER `color_code`;

INSERT INTO `awsdev`.`all_pages` (`TabID`, `TabName`, `TabLink`, `Status`, `Order`) VALUES ('7', 'Server Recovery', 'ServerRecovery', '1', '5');
INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('154', 'Server Recovery', 'ServerRecovery', '', '', '1', '', '1', '', '0', '6', '0');

INSERT INTO `awsdev`.`all_pages` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `GroupName`, `Order`) VALUES ('9', 'Media Recovery', 'MediaRecovery', 'MediaRecovery', '', '1', '', '', '8');
INSERT INTO `awsdev`.`all_pages` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `GroupName`, `Order`) VALUES ('9', 'Pending Media', 'PendingMedia', 'PendingMedia', '', '1', '', '', '9');

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('155', 'Media Recovery', 'MediaRecovery', 'MediaRecovery', '', '1', '', '1', '', '0', '8', '0');
INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('155', 'Pending Media', 'PendingMedia', 'PendingMedia', '', '1', '', '1', '', '0', '9', '0');

ALTER TABLE awsprod`.`asset ADD COLUMN ediflag INT NULL DEFAULT 0 AFTER clean_receive_flag;


ALTER TABLE `awsdev`.`shipping_containers` ADD COLUMN `PalletID` VARCHAR(100) NULL DEFAULT NULL AFTER `ShippingControllerLoginID`;


INSERT INTO `awsdev`.`all_tabs` (`TabName`, `TabLink`, `Description`, `Status`, `CSSClass`, `Dashboard`, `Order`) VALUES ('Audit', 'speed', 'speed', '1', '', '0', '3');
INSERT INTO `awsdev`.`all_pages` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `Order`) VALUES ('35', 'Inbound Storage Audit', 'StorageAudit', 'StorageAudit', '1', '1');
INSERT INTO `awsdev`.`tabs` (`TabName`, `TabLink`, `Description`, `AccountID`, `Status`, `Dashboard`, `Order`, `TabIcon`) VALUES ('Audit', 'speed', 'speed', '1', '1', '0', '3', 'find_in_page');
INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('162', 'Inbound Storage Audit', 'StorageAudit', 'StorageAudit', '1', '1', '0', '1', '0');

ALTER TABLE `awsdev`.`users` ADD COLUMN `AuditController` TINYINT NULL DEFAULT 0 AFTER `RemovalController`;


ALTER TABLE `awsdev`.`pallets`
ADD COLUMN `AuditControllerLoginID` VARCHAR(100) NULL DEFAULT NULL AFTER `idCustomer`,
ADD COLUMN `InspectionResult` VARCHAR(100) NULL DEFAULT NULL AFTER `AuditControllerLoginID`,
ADD COLUMN `InspectionAuditDateTime` DATETIME NULL DEFAULT NULL AFTER `InspectionResult`,
ADD COLUMN `InspectionAuditBy` INT NULL DEFAULT NULL AFTER `InspectionAuditDateTime`;


CREATE TABLE `awsdev`.`storage_audit_records` (
  `AuditID` INT NOT NULL AUTO_INCREMENT,
  `idPallet` VARCHAR(50) NULL DEFAULT NULL,
  `LocationID` INT NULL DEFAULT NULL,
  `AuditControllerLoginID` VARCHAR(100) NULL DEFAULT NULL,
  `InspectionResult` VARCHAR(100) NULL DEFAULT NULL,
  `InspectionAuditDateTime` DATETIME NULL DEFAULT NULL,
  `InspectionAuditBy` INT NULL DEFAULT NULL,
  `valid_storage_location_flag` VARCHAR(10) NULL DEFAULT NULL,
  `valid_seal_flag` VARCHAR(10) NULL DEFAULT NULL,
  `valid_tamper_flag` VARCHAR(10) NULL DEFAULT NULL,
  `clean_audit_flag` VARCHAR(10) NULL DEFAULT NULL,
  PRIMARY KEY (`AuditID`));


  ALTER TABLE `awsdev`.`shipping_containers`
ADD COLUMN `FacilityID` INT NULL DEFAULT NULL AFTER `PalletID`,
ADD COLUMN `LocationID` INT NULL DEFAULT NULL AFTER `FacilityID`;

INSERT INTO `awsdev`.`all_pages` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `Order`) VALUES ('35', 'Outbound Storage Audit', 'OutboundStorageAudit', 'OutboundStorageAudit', '1', '2');
INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('162', 'Outbound Storage Audit', 'OutboundStorageAudit', 'OutboundStorageAudit', '1', '1', '0', '2', '0');
INSERT INTO `awsdev`.`shipping_status` (`ShipmentStatusID`, `Status`, `StatusDescritption`) VALUES ('7', 'Quarantine', 'Quarantine');

ALTER TABLE `awsdev`.`storage_audit_records` ADD COLUMN `ShippingContainerID` VARCHAR(50) NULL DEFAULT NULL AFTER `ContainerPackage`;

ALTER TABLE `awsdev`.`shipping_containers`
ADD COLUMN `InspectionResult` VARCHAR(100) NULL DEFAULT NULL AFTER `LocationID`,
ADD COLUMN `InspectionAuditDateTime` DATETIME NULL DEFAULT NULL AFTER `InspectionResult`,
ADD COLUMN `InspectionAuditBy` INT NULL DEFAULT NULL AFTER `InspectionAuditDateTime`,
ADD COLUMN `AuditControllerLoginID` VARCHAR(100) NULL DEFAULT NULL AFTER `InspectionAuditBy`;



CREATE TABLE `awsdev`.`server_recovery_records` (
  `AuditID` INT NOT NULL AUTO_INCREMENT,
  `idPallet` VARCHAR(50) NULL DEFAULT NULL,
  `LocationID` INT NULL DEFAULT NULL,
  `AuditControllerLoginID` VARCHAR(100) NULL DEFAULT NULL,
  `InspectionResult` VARCHAR(100) NULL DEFAULT NULL,
  `InspectionAuditDateTime` DATETIME NULL DEFAULT NULL,
  `InspectionAuditBy` INT NULL DEFAULT NULL,
  `valid_storage_location_flag` VARCHAR(10) NULL DEFAULT NULL,
  `valid_seal_flag` VARCHAR(10) NULL DEFAULT NULL,
  `valid_tamper_flag` VARCHAR(10) NULL DEFAULT NULL,
  `clean_audit_flag` VARCHAR(10) NULL DEFAULT NULL,
  PRIMARY KEY (`AuditID`));



CREATE TABLE `awsdev`.`speed_expected_servers` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `idPallet` VARCHAR(50) NULL DEFAULT NULL,
  `SerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `MPN` VARCHAR(100) NULL DEFAULT NULL,
  `Type` VARCHAR(20) NULL DEFAULT NULL,
  `ServerID` VARCHAR(50) NULL DEFAULT NULL,
  `APICalledDate` DATETIME NULL DEFAULT NULL,
  `APICalledBy` INT NULL DEFAULT NULL,
  `ReceivedDate` DATETIME NULL DEFAULT NULL,
  `ReceivedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ID`));

ALTER TABLE `awsdev`.`pallets`
ADD COLUMN `ServersCreatedDate` DATETIME NULL DEFAULT NULL AFTER `InspectionAuditBy`;

ALTER TABLE `awsdev`.`pallets`
ADD COLUMN `ServersCreatedBy` INT NULL DEFAULT NULL AFTER `ServersCreatedDate`;

After Feb Release
------------------


INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Print Label', 'PrintLabel', '', '', '1', '', '1', 'eViridis Administration', '0', '64', '0');


CREATE TABLE `awsdev`.`speed_expected_media` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `idPallet` VARCHAR(50) NULL DEFAULT NULL,
  `ServerSerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `MediaSerialNumber` VARCHAR(100) NULL DEFAULT NULL,
  `MediaType` VARCHAR(50) NULL DEFAULT NULL,
  `MediaMPN` VARCHAR(100) NULL DEFAULT NULL,
  `APICalledDate` DATETIME NULL DEFAULT NULL,
  `APICalledBy` INT NULL DEFAULT NULL,
  `ReceivedDate` DATETIME NULL DEFAULT NULL,
  `ReceivedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ID`));


  CREATE TABLE `awsdev`.`speed_server_recovery` (
  `ServerID` INT NOT NULL AUTO_INCREMENT,
  `idPallet` VARCHAR(50) NULL DEFAULT NULL,
  `ServerSerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `Type` VARCHAR(50) NULL DEFAULT NULL,
  `MPN` VARCHAR(100) NULL DEFAULT NULL,
  `disposition_id` INT NULL DEFAULT NULL,
  `CustomPalletID` INT NULL DEFAULT NULL,
  `StatusID` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  `AuditControllerID` VARCHAR(100) NULL DEFAULT NULL,
  PRIMARY KEY (`ServerID`));


  ALTER TABLE `awsstg`.`speed_server_recovery`
ADD INDEX `server_recovery_bin_idx` (`CustomPalletID` ASC) VISIBLE;
ALTER TABLE `awsstg`.`speed_server_recovery` ALTER INDEX `server_recovery_to_pallet_idx` INVISIBLE;
ALTER TABLE `awsstg`.`speed_server_recovery`
ADD CONSTRAINT `server_recovery_bin`
  FOREIGN KEY (`CustomPalletID`)
  REFERENCES `awsstg`.`custompallet` (`CustomPalletID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

  ALTER TABLE `awsdev`.`custompallet_items` ADD COLUMN `ServerID` INT NULL DEFAULT NULL AFTER `status`;



  CREATE TABLE `awsdev`.`speed_media_recovery` (
  `MediaID` INT NOT NULL AUTO_INCREMENT,
  `idPallet` VARCHAR(50) NULL DEFAULT NULL,
  `ServerSerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `MediaSerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `MediaType` VARCHAR(50) NULL DEFAULT NULL,
  `MediaMPN` VARCHAR(100) NULL DEFAULT NULL,
  `disposition_id` INT NULL DEFAULT NULL,
  `CustomPalletID` INT NULL DEFAULT NULL,
  `StatusID` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  `AuditControllerID` VARCHAR(50) NULL DEFAULT NULL,
  PRIMARY KEY (`MediaID`));


  ALTER TABLE `awsdev`.`custompallet_items` ADD COLUMN `MediaID` INT NULL DEFAULT NULL AFTER `ServerID`;

  ALTER TABLE `awsdev`.`speed_media_recovery` ADD COLUMN `VerificationID` VARCHAR(50) NULL DEFAULT NULL AFTER `AuditControllerID`;
  INSERT INTO `awsdev`.`asset_status` (`StatusID`, `Status`) VALUES ('9', 'Recovered');

  rename table labor_tracking to bk_labor_tracking

  CREATE TABLE `awsdev`.`labor_tracking` (
  `labor_track_id` INT NOT NULL AUTO_INCREMENT,
  `user_id` INT NOT NULL,
  `user_facility` INT NOT NULL,
  `event_date` DATE NOT NULL,
  `workflow_id` INT NOT NULL,
  `event_duration` VARCHAR(45) NOT NULL,
  `record_date` DATETIME NOT NULL,
  `created_by` INT NOT NULL,
  `submitted_date` DATETIME NULL,
  `submitted_by` INT NULL,
  `is_submitted` VARCHAR(45) NULL DEFAULT 0,
  PRIMARY KEY (`labor_track_id`));

  ALTER TABLE `awsdev`.`site` ADD COLUMN `workflow_id` INT NULL DEFAULT NULL AFTER `LockedForUser`;


ALTER TABLE `awsstg`.`site`
ADD INDEX `stationtoworkflow_idx` (`workflow_id` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`site`
ADD CONSTRAINT `stationtoworkflow`
  FOREIGN KEY (`workflow_id`)
  REFERENCES `awsstg`.`workflow` (`workflow_id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

INSERT INTO `awsstg`.`workflow` (`workflow_id`, `workflow`, `workflow_description`, `status`) VALUES ('8', 'Server Recovery', 'Server Recovery', 'Active');
INSERT INTO `awsstg`.`workflow` (`workflow_id`, `workflow`, `workflow_description`, `status`) VALUES ('9', 'Media Recovery', 'Media Recovery', 'Active');

CREATE TABLE `awsdev`.`station_disposition_mapping` (
  `map_id` INT NOT NULL AUTO_INCREMENT,
  `SiteID` INT NULL DEFAULT NULL,
  `disposition_id` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`map_id`));

  ALTER TABLE `awsdev`.`station_disposition_mapping`
ADD INDEX `mappingtosite_idx` (`SiteID` ASC) VISIBLE,
ADD INDEX `mappingtodisposition_idx` (`disposition_id` ASC) VISIBLE;
;
ALTER TABLE `awsdev`.`station_disposition_mapping`
ADD CONSTRAINT `mappingtosite`
  FOREIGN KEY (`SiteID`)
  REFERENCES `awsdev`.`site` (`SiteID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `mappingtodisposition`
  FOREIGN KEY (`disposition_id`)
  REFERENCES `awsdev`.`disposition` (`disposition_id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

  ALTER TABLE `awsdev`.`workflow_labor_tracking`
ADD COLUMN `labor_type` ENUM('Direct', 'Indirect') NULL AFTER `Status`,
ADD COLUMN `minHours` TINYINT(4) NULL DEFAULT NULL AFTER `labor_type`,
ADD COLUMN `minMinutes` DECIMAL(3,2) NULL AFTER `minHours`,
ADD COLUMN `maxHours` TINYINT(4) NULL AFTER `minMinutes`,
ADD COLUMN `maxMinutes` DECIMAL(3,2) NULL AFTER `maxHours`;

ALTER TABLE `awsdev`.`profile_type`
ADD COLUMN `LaborTrackingRequired` TINYINT(4) NULL DEFAULT '0' AFTER `VendorBased`;

INSERT INTO `awsdev`.`left_menus` (`MenuID`, `TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('', '153', 'Pending Labor Tracker', 'PendingLaborTracker', 'Pending labor tracker', '', '1', '', '1', 'eViridis Administration', '0', '58', '0');

ALTER TABLE `awsdev`.`labor_tracking`
ADD COLUMN `override_reason` VARCHAR(45) NULL DEFAULT NULL AFTER `is_submitted`;

ALTER TABLE `awsdev`.`users`
ADD COLUMN `ServerRecoveryController` TINYINT NULL DEFAULT 0 AFTER `AuditController`,
ADD COLUMN `MediaRecoveryController` TINYINT NULL DEFAULT 0 AFTER `ServerRecoveryController`;


ALTER TABLE `awsdev`.`customer` ADD COLUMN `SourceCategory` INT NULL DEFAULT NULL AFTER `InboundWeightFee`;

UPDATE `awsstg`.`vendor_category` SET `VendorCategoryName` = 'Facility' WHERE (`idvendor_category` = '12');
UPDATE `awsstg`.`vendor_category` SET `VendorCategoryName` = 'VendorFailureAnalysis' WHERE (`idvendor_category` = '14');

CREATE TABLE `labor_tracker_pending` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `EventDate` DATETIME NOT NULL,
  `UserId` INT NOT NULL,
  `FacilityID` INT NULL,
  `ProfileID` INT NULL,
  `Duration` DECIMAL(4,2) NULL,
  `CreatedDate` DATETIME NULL,
  PRIMARY KEY (`id`));

  ALTER TABLE `labor_tracking`
CHANGE COLUMN `user_facility` `user_facility` INT NULL ,
CHANGE COLUMN `workflow_id` `workflow_id` INT NULL ,
CHANGE COLUMN `event_duration` `event_duration` VARCHAR(45) NULL ,
CHANGE COLUMN `record_date` `record_date` DATETIME NULL ;

ALTER TABLE `awsdev`.`labor_tracking`
CHANGE COLUMN `is_submitted` `is_submitted` TINYINT(4) NULL DEFAULT '0' ;

ALTER TABLE `awsdev`.`labor_tracker_pending`
CHANGE COLUMN `EventDate` `EventDate` DATE NOT NULL ;

After April Release
--------------------


CREATE TABLE `awsdev`.`speed_media_process` (
  `ProcessID` INT NOT NULL AUTO_INCREMENT,
  `MediaID` INT NULL DEFAULT NULL,
  `idPallet` VARCHAR(50) NULL DEFAULT NULL,
  `ServerSerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `MediaSerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `MediaType` VARCHAR(50) NULL DEFAULT NULL,
  `from_disposition_id` INT NULL DEFAULT NULL,
  `to_disposition_id` INT NULL DEFAULT NULL,
  `from_status` INT NULL DEFAULT NULL,
  `to_status` INT NULL DEFAULT NULL,
  `from_CustomPalletID` INT NULL DEFAULT NULL,
  `to_CustomPalletID` INT NULL DEFAULT NULL,
  `AuditControllerID` VARCHAR(50) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `ProcessType` VARCHAR(50) NULL DEFAULT NULL,
  PRIMARY KEY (`ProcessID`));


  ALTER TABLE `awsdev`.`disposition`
ADD COLUMN `switch_disposition` TINYINT NULL DEFAULT 0 AFTER `color`,
ADD COLUMN `server_disposition` TINYINT NULL DEFAULT 0 AFTER `switch_disposition`;


INSERT INTO `awsdev`.`disposition` (`disposition`, `disposition_description`, `status`, `sub_disposition`, `switch_disposition`, `server_disposition`) VALUES ('Terminal-ServerRecycleProcessed', 'Terminal-ServerRecycleProcessed', 'Active', '0', '1', '0');
INSERT INTO `awsdev`.`disposition` (`disposition`, `disposition_description`, `status`, `shipping_removal_type`, `sub_disposition`, `switch_disposition`, `server_disposition`) VALUES ('Temporary-MediaRecovery', 'Temporary-MediaRecovery', 'Active', '0', '0', '0', '1');

ALTER TABLE `awsdev`.`disposition`
ADD COLUMN `hdd_disposition` TINYINT NULL DEFAULT 0 AFTER `server_disposition`,
ADD COLUMN `ssd_disposition` TINYINT NULL DEFAULT 0 AFTER `hdd_disposition`;


UPDATE `awsdev`.`disposition` SET `hdd_disposition` = '1' WHERE (`disposition_id` = '35'); (This should be for Temporary-MediaDegauss-SC sub-disposition)
UPDATE `awsdev`.`disposition` SET `ssd_disposition` = '1' WHERE (`disposition_id` = '36'); (This should be for Terminal-MediaDestruction-SC sub-disposition)
And for Temporary-MediaRecovery disposition, server_disposition should be 1 and Terminal-ServerRecycleProcessed disposition switch_disposition should be 1

ALTER TABLE `awsdev`.`users` ADD COLUMN `PendingMediaController` TINYINT NULL DEFAULT 0 AFTER `MediaRecoveryController`;

ALTER TABLE `awsdev`.`disposition` ADD COLUMN `destroyed_disposition` TINYINT NULL DEFAULT 0 AFTER `ssd_disposition`;

UPDATE `awsdev`.`disposition` SET `destroyed_disposition` = '1' WHERE (`disposition_id` = '37'); (This should be for Destroyed-SC sub-disposition);


CREATE TABLE `awsdev`.`speed_server_recovery_tracking` (
  `TrackID` INT NOT NULL AUTO_INCREMENT,
  `ServerID` INT NULL DEFAULT NULL,
  `ServerSerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `Type` VARCHAR(50) NULL DEFAULT NULL,
  `idPallet` VARCHAR(50) NULL DEFAULT NULL,
  `Action` TEXT NULL DEFAULT NULL,
  `Description` TEXT NULL DEFAULT NULL,
  `ControllerLoginID` VARCHAR(50) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `Table` VARCHAR(100) NULL DEFAULT NULL,
  `ReferenceID` VARCHAR(100) NULL DEFAULT NULL,
  `RequestName` VARCHAR(100) NULL DEFAULT NULL,
  PRIMARY KEY (`TrackID`));

  ALTER TABLE `awsdev`.`speed_server_recovery_tracking`
ADD INDEX `serveridindex` (`ServerID` ASC) INVISIBLE,
ADD INDEX `serverserialindex` (`ServerSerialNumber` ASC) VISIBLE;
;


CREATE TABLE `awsdev`.`speed_media_recovery_tracking` (
  `TrackID` INT NOT NULL AUTO_INCREMENT,
  `MediaID` INT NULL DEFAULT NULL,
  `MediaSerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `MediaType` VARCHAR(50) NULL DEFAULT NULL,
  `ServerSerialNumber` VARCHAR(50) NULL DEFAULT NULL,
  `ServerID` INT NULL DEFAULT NULL,
  `idPallet` VARCHAR(50) NULL DEFAULT NULL,
  `Action` TEXT NULL DEFAULT NULL,
  `Description` TEXT NULL DEFAULT NULL,
  `ControllerLoginID` VARCHAR(50) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `Table` VARCHAR(100) NULL DEFAULT NULL,
  `ReferenceID` VARCHAR(100) NULL DEFAULT NULL,
  `RequestName` VARCHAR(100) NULL DEFAULT NULL,
  PRIMARY KEY (`TrackID`));


ALTER TABLE `awsdev`.`speed_media_recovery_tracking`
ADD INDEX `speedmediaidtoid_idx` (`MediaID` ASC) VISIBLE;
;
ALTER TABLE `awsdev`.`speed_media_recovery_tracking`
ADD CONSTRAINT `speedmediaidtoid`
  FOREIGN KEY (`MediaID`)
  REFERENCES `awsdev`.`speed_media_recovery` (`MediaID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

  ALTER TABLE `awsstg`.`speed_media_recovery_tracking`
ADD INDEX `serialnumberindex` (`MediaSerialNumber` ASC) VISIBLE;
;

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('156', 'SPEED Tracking', 'SpeedTracking', 'SpeedTracking', '1', '1', '0', '10', '0');
ALTER TABLE `awsstg`.`shipping_containers` ADD COLUMN `disposition_id` INT NULL DEFAULT NULL AFTER `AuditControllerLoginID`;


ALTER TABLE `awsdev`.`shipping_containers`
DROP FOREIGN KEY `shippingcontainerstoshipping`;
ALTER TABLE `awsdev`.`shipping_containers`
CHANGE COLUMN `ShippingID` `ShippingID` VARCHAR(50) NULL DEFAULT NULL ;
ALTER TABLE `awsdev`.`shipping_containers`
ADD CONSTRAINT `shippingcontainerstoshipping`
  FOREIGN KEY (`ShippingID`)
  REFERENCES `awsdev`.`shipping` (`ShippingID`);


  ALTER TABLE `awsdev`.`shipping_container_serials` ADD COLUMN `ServerID` INT NULL DEFAULT NULL AFTER `ControllerLoginID`,ADD COLUMN `ServerSerialNumber` VARCHAR(50) NULL DEFAULT NULL AFTER `ServerID`;

  ALTER TABLE `awsstg`.`shipping_container_serials` ADD INDEX `shippingserverserialindex` (`ServerSerialNumber` ASC) VISIBLE;;


  CREATE TABLE `awsstg`.`by_products` (
  `byproduct_id` INT NOT NULL AUTO_INCREMENT,
  `part_type` VARCHAR(100) NULL DEFAULT NULL,
  `disposition_id` INT NULL DEFAULT NULL,
  `description` TEXT NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  `StatusID` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`byproduct_id`));


  ALTER TABLE `awsdev`.`by_products`
ADD INDEX `by_productstofacility_idx` (`FacilityID` ASC) VISIBLE,
ADD INDEX `by_producttodisposition_idx` (`disposition_id` ASC) VISIBLE;
;
ALTER TABLE `awsdev`.`by_products`
ADD CONSTRAINT `by_productstofacility`
  FOREIGN KEY (`FacilityID`)
  REFERENCES `awsdev`.`facility` (`FacilityID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `by_producttodisposition`
  FOREIGN KEY (`disposition_id`)
  REFERENCES `awsdev`.`disposition` (`disposition_id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `awsdev`.`shipping_container_serials` ADD COLUMN `byproduct_id` INT NULL DEFAULT NULL AFTER `ServerSerialNumber`;

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `Order`, `ReadonlyEnabled`) VALUES ('158', 'Shipment Container', 'ShipmentContainer', 'ShipmentContainer', '1', '1', '12', '0');

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Container Type', 'ContainerTypeList', '1', '1', 'eViridis Administration', '0', '65', '0');

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Byproduct', 'ByProductsList', '1', '1', 'eViridis Administration', '0', '66', '0');


ALTER TABLE `awsdev`.`facility`
ADD COLUMN `WeightUnit` VARCHAR(10) CHARACTER SET 'armscii8' NULL DEFAULT NULL AFTER `ProfitCenter`,
ADD COLUMN `DimensionUnit` VARCHAR(10) NULL DEFAULT NULL AFTER `WeightUnit`,
ADD COLUMN `Currency` VARCHAR(10) NULL DEFAULT NULL AFTER `DimensionUnit`;


ALTER TABLE `awsstg`.`package`
ADD COLUMN `dimensions` VARCHAR(55) NULL AFTER `type`,
ADD COLUMN `providerid` VARCHAR(50) NULL AFTER `dimensions`,
ADD COLUMN `containercost` VARCHAR(55) NULL AFTER `providerid`,
ADD COLUMN `Usage` VARCHAR(55) NULL AFTER `containercost`,
ADD COLUMN `FacilityID` INT NULL AFTER `Usage`;



CREATE TABLE `awsdev`.`Carrier` (
  `CarrierID` INT NOT NULL AUTO_INCREMENT,
  `CarrierName` VARCHAR(100) NULL,
  `FacilityID` INT NULL,
  `StatusID` INT NULL,
  `CreatedDate` DATETIME NULL,
  `CreatedBy` INT NULL,
  `UpdatedDate` DATETIME NULL,
  `UpdatedBy` INT NULL,
  PRIMARY KEY (`CarrierID`));


  ALTER TABLE `awsdev`.`Carrier`
ADD INDEX `carriertofacility_idx` (`FacilityID` ASC) VISIBLE;
;
ALTER TABLE `awsdev`.`Carrier`
ADD CONSTRAINT `carriertofacility`
  FOREIGN KEY (`FacilityID`)
  REFERENCES `awsdev`.`facility` (`FacilityID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


  INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Carrier', 'CarrierList', '1', '1', 'eViridis Administration', '0', '67', '0');


  ALTER TABLE `awsdev`.`shipping`
ADD COLUMN `CarrierID` INT NULL DEFAULT NULL AFTER `FacilityID`,
ADD COLUMN `VehicleID` VARCHAR(50) NULL DEFAULT NULL AFTER `CarrierID`,
ADD COLUMN `Trailer` VARCHAR(50) NULL DEFAULT NULL AFTER `VehicleID`,
ADD COLUMN `TrailerSeal` VARCHAR(50) NULL DEFAULT NULL AFTER `Trailer`,
ADD COLUMN `ShipmentTracking` VARCHAR(50) CHARACTER SET 'ascii' NULL DEFAULT NULL AFTER `TrailerSeal`,
ADD COLUMN `PONumber` VARCHAR(50) NULL DEFAULT NULL AFTER `ShipmentTracking`;


ALTER TABLE `awsstg`.`shipping`
ADD INDEX `shippingtocarrier_idx` (`CarrierID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`shipping`
ADD CONSTRAINT `shippingtocarrier`
  FOREIGN KEY (`CarrierID`)
  REFERENCES `awsstg`.`Carrier` (`CarrierID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `awsstg`.`vendor` ADD COLUMN `WasteCertificationID` VARCHAR(50) NULL DEFAULT NULL AFTER `ContainerMPNLock`;
update awsdev.vendor set WasteCertificationID = 'n/a' where 1;


CREATE TABLE `awsstg`.`removal_codes` (
  `RemovalCodeID` INT NOT NULL AUTO_INCREMENT,
  `RemovalCode` VARCHAR(50) NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  `disposition_id` INT NULL DEFAULT NULL,
  `InventoryType` VARCHAR(50) NULL DEFAULT NULL,
  `part_type` VARCHAR(50) NULL DEFAULT NULL,
  `Description` TEXT NULL DEFAULT NULL,
  `StatusID` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`RemovalCodeID`));


  ALTER TABLE `awsstg`.`removal_codes`
ADD INDEX `removalcodetofacility_idx` (`FacilityID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`removal_codes`
ADD CONSTRAINT `removalcodetofacility`
  FOREIGN KEY (`FacilityID`)
  REFERENCES `awsstg`.`facility` (`FacilityID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Removal Code', 'RemovalCodeList', '1', '1', 'eViridis Administration', '0', '68', '0');

ALTER TABLE `awsstg`.`package` CHANGE COLUMN `providerid` `providerid` VARCHAR(50) NULL DEFAULT NULL ;

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Server/Switch Consolidation', 'ServerConsolidation', '1', '1', 'eViridis Administration', '0', '60', '0');

ALTER TABLE `awsdev`.`bin_consolidation_asset_movement` ADD COLUMN `ServerID` INT NULL DEFAULT NULL AFTER `idPallet`;

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Media Consolidation', 'MediaConsolidation', '1', '1', 'eViridis Administration', '0', '60', '0');
ALTER TABLE `awsdev`.`bin_consolidation_asset_movement` ADD COLUMN `MediaID` INT NULL DEFAULT NULL AFTER `ServerID`;

ALTER TABLE `awsstg`.`speed_expected_servers`
ADD COLUMN `HDDCount` INT NULL DEFAULT NULL AFTER `ReceivedBy`,
ADD COLUMN `SSDCount` INT NULL DEFAULT NULL AFTER `HDDCount`;


ALTER TABLE `awsstg`.`package` ADD COLUMN `ContainerClassification` VARCHAR(50) NULL DEFAULT NULL AFTER `FacilityID`;
update awsstg.package set ContainerClassification = 'Inbound' where isnull(ContainerClassification);

ALTER TABLE `awsdev`.`shipping` ADD COLUMN `RemovalController` VARCHAR(100) NULL DEFAULT NULL AFTER `PONumber`;
ALTER TABLE `awsdev`.`speed_expected_servers` ADD COLUMN `IPN` VARCHAR(100) NULL DEFAULT NULL AFTER `SSDCount`;


CREATE TABLE `awsdev`.`speed_sns_messages` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `Input` TEXT NULL DEFAULT NULL,
  `OutPut` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `awsstg`.`pallets` ADD COLUMN `ReceivedBy` INT NULL DEFAULT NULL AFTER `ReceivedDate`;

ALTER TABLE `awsstg`.`speed_server_recovery`
ADD COLUMN `SiteID` INT NULL DEFAULT NULL AFTER `ActualSerialNumber`,
ADD COLUMN `valid_rack_flag` VARCHAR(10) NULL DEFAULT NULL AFTER `SiteID`;


ALTER TABLE `awsdev`.`speed_media_recovery`
ADD COLUMN `valid_identity_flag` VARCHAR(5) NULL DEFAULT NULL COMMENT 'IF MPN matches to API MPN' AFTER `ActualSerialNumber`,
ADD COLUMN `valid_host_flag` VARCHAR(5) NULL DEFAULT NULL COMMENT 'If Media available in API' AFTER `valid_identity_flag`;

ALTER TABLE `awsdev`.`speed_media_recovery` ADD COLUMN `WIPSourceLocationID` INT NULL DEFAULT NULL AFTER `valid_host_flag`;

ALTER TABLE `awsdev`.`speed_media_recovery`
ADD COLUMN `WIPSourceBinName` TEXT NULL DEFAULT NULL AFTER `WIPSourceLocationID`,
ADD COLUMN `ServerBinID` INT NULL DEFAULT NULL AFTER `WIPSourceBinName`,
ADD COLUMN `ServerBinName` TEXT NULL DEFAULT NULL COMMENT 'Server moved to CP, after Media recovery.' AFTER `ServerBinID`;


ALTER TABLE `awsdev`.`shipping_containers`
ADD COLUMN `RecentSealDate` DATETIME NULL DEFAULT NULL AFTER `disposition_id`,
ADD COLUMN `RecentSealBy` INT NULL DEFAULT NULL AFTER `RecentSealDate`;

ALTER TABLE `awsdev`.`speed_media_process` ADD COLUMN `ScannerID` TEXT NULL DEFAULT NULL AFTER `ActualSerialNumber`;

ALTER TABLE `awsdev`.`pallets` ADD COLUMN `rackIpn` TEXT NULL DEFAULT NULL AFTER `ServersCreatedBy`;


ALTER TABLE `awsdev`.`speed_sns_messages`
ADD COLUMN `event_type` VARCHAR(100) NULL DEFAULT NULL AFTER `CreatedBy`,
ADD COLUMN `MessageID` TEXT NULL DEFAULT NULL AFTER `event_type`,
ADD COLUMN `SNSResult` VARCHAR(10) NULL DEFAULT NULL AFTER `MessageID`,
ADD COLUMN `FailMessage` TEXT NULL DEFAULT NULL AFTER `SNSResult`,
ADD COLUMN `SuccessMessage` TEXT NULL DEFAULT NULL AFTER `FailMessage`,
ADD COLUMN `ScannerName` TEXT NULL DEFAULT NULL AFTER `SuccessMessage`,
ADD COLUMN `idPallet` VARCHAR(100) NULL DEFAULT NULL AFTER `ScannerName`,
ADD COLUMN `ServerSerialNumber` VARCHAR(100) NULL DEFAULT NULL AFTER `idPallet`,
ADD COLUMN `MediaID` INT NULL DEFAULT NULL AFTER `ServerSerialNumber`,
ADD COLUMN `MediaSerialNumber` VARCHAR(100) NULL DEFAULT NULL AFTER `MediaID`,
ADD COLUMN `MediaType` VARCHAR(20) NULL DEFAULT NULL AFTER `MediaSerialNumber`,
ADD COLUMN `NextBinID` VARCHAR(100) NULL DEFAULT NULL AFTER `MediaType`;

ALTER TABLE `awsdev`.`speed_sns_messages` ADD COLUMN `ServerID` INT NULL DEFAULT NULL AFTER `NextBinID`;

ALTER TABLE `awsstg`.`asset_repair` ADD COLUMN `ActualSerialNumber` VARCHAR(100) NULL DEFAULT NULL AFTER `repair_type`;

CREATE TABLE `awsdev`.`speed_rackdetails_api` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `idPallet` VARCHAR(100) NULL DEFAULT NULL,
  `Input` TEXT NULL DEFAULT NULL,
  `Output` TEXT NULL DEFAULT NULL,
  `Result` VARCHAR(10) NULL DEFAULT NULL,
  `APICalledDATETIME` DATETIME NULL DEFAULT NULL,
  `APICalledBy` INT NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ID`));


  ALTER TABLE `awsstg`.`speed_rackdetails_api` ADD COLUMN `rackIpn` VARCHAR(100) NULL DEFAULT NULL AFTER `FacilityID`,ADD COLUMN `site` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Response from API' AFTER `rackIpn`;

  CREATE TABLE awsstg.`speed_rackdetails_api_servers` (
  `API_SERVER_ID` int NOT NULL AUTO_INCREMENT,
  `ID` int DEFAULT NULL,
  `idPallet` varchar(50) DEFAULT NULL,
  `SerialNumber` varchar(50) DEFAULT NULL,
  `MPN` varchar(100) DEFAULT NULL,
  `Type` varchar(20) DEFAULT NULL,
  `ServerID` varchar(50) DEFAULT NULL,
  `APICalledDate` datetime DEFAULT NULL,
  `APICalledBy` int DEFAULT NULL,
  `ReceivedDate` datetime DEFAULT NULL,
  `ReceivedBy` int DEFAULT NULL,
  `HDDCount` int DEFAULT NULL,
  `SSDCount` int DEFAULT NULL,
  `IPN` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`API_SERVER_ID`),
  KEY `idpallet_idx` (`idPallet`)
);

CREATE TABLE awsstg.`speed_rackdetails_api_media` (
  `API_MEDIA_ID` int NOT NULL AUTO_INCREMENT,
  `API_SERVER_ID` int DEFAULT NULL,
  `idPallet` varchar(50) DEFAULT NULL,
  `ServerSerialNumber` varchar(50) DEFAULT NULL,
  `MediaSerialNumber` varchar(100) DEFAULT NULL,
  `MediaType` varchar(50) DEFAULT NULL,
  `MediaMPN` varchar(100) DEFAULT NULL,
  `APICalledDate` datetime DEFAULT NULL,
  `APICalledBy` int DEFAULT NULL,
  `ReceivedDate` datetime DEFAULT NULL,
  `ReceivedBy` int DEFAULT NULL,
  PRIMARY KEY (`API_MEDIA_ID`)
);

ALTER TABLE `awsstg`.`speed_media_recovery` ADD COLUMN `origin_bin_id` TEXT NULL DEFAULT NULL COMMENT '“Bin ID” assigned to “Media Out SN” in Media Recovery Screen' AFTER `ServerBinName`;

ALTER TABLE `awsdev`.`speed_rackdetails_api_servers` ADD COLUMN `intact` VARCHAR(10) NULL DEFAULT NULL AFTER `IPN`;


ALTER TABLE `awsstg`.`speed_expected_servers` ADD COLUMN `intact` VARCHAR(10) NULL DEFAULT NULL AFTER `IPN`;


After 22-8-2-23
---------------
ALTER TABLE `awsdev`.`catlog_creation` CHANGE COLUMN `part_type` `part_type` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;
ALTER TABLE `awsdev`.`disposition` CHANGE COLUMN `disposition` `disposition` VARCHAR(50) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;
ALTER TABLE `awsdev`.`disposition` CHANGE COLUMN `disposition_description` `disposition_description` TEXT CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;
ALTER TABLE `awsdev`.`SanitizationType` CHANGE COLUMN `SanitizationType` `SanitizationType` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;
ALTER TABLE `awsdev`.`business_rule_attribute_values` CHANGE COLUMN `value` `value` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;
ALTER TABLE `awsdev`.`business_rule_attribute_values` CHANGE COLUMN `description` `description` TEXT CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;


ALTER TABLE `awsdev`.`workflow` CHANGE COLUMN `workflow` `workflow` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;
ALTER TABLE `awsdev`.`Rig`
CHANGE COLUMN `Rigname` `Rigname` VARCHAR(250) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `Description` `Description` VARCHAR(250) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;
ALTER TABLE `awsdev`.`disposition` CHANGE COLUMN `status` `status` VARCHAR(50) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;
ALTER TABLE `awsstg`.`disposition` CHANGE COLUMN `color` `color` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;

ALTER TABLE `awsdev`.`business_rule_attribute_values`
CHANGE COLUMN `status` `status` VARCHAR(10) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT 'Active' ,
CHANGE COLUMN `file_url` `file_url` TEXT CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;

ALTER TABLE `awsstg`.`business_rule`
CHANGE COLUMN `rule_name` `rule_name` VARCHAR(200) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `rule_description` `rule_description` TEXT CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `status` `status` VARCHAR(50) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;

ALTER TABLE `awsstg`.`catlog_creation`
CHANGE COLUMN `sanitization_flag` `sanitization_flag` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `nd_sanitization_type` `nd_sanitization_type` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `demand_type` `demand_type` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `fa_capability_type` `fa_capability_type` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;

ALTER TABLE `awsdev`.`catlog_creation`
CHANGE COLUMN `rma_type` `rma_type` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `liquidation_type` `liquidation_type` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `harvest_type` `harvest_type` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;

ALTER TABLE `awsdev`.`catlog_creation`
CHANGE COLUMN `RepairType` `RepairType` VARCHAR(100) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;



CREATE TABLE `awsdev`.`speed_bulk_media_process` (
  `BulkMediaProcessID` INT NOT NULL AUTO_INCREMENT,
  `MediaType` VARCHAR(10) NULL DEFAULT NULL,
  `NextStatus` VARCHAR(100) NULL DEFAULT NULL,
  `NewCustomPalletID` INT NULL DEFAULT NULL,
  `ControllerLoginID` VARCHAR(100) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `Status` VARCHAR(50) NULL DEFAULT 'Pending',
  `Description` TEXT NULL DEFAULT NULL,
  PRIMARY KEY (`BulkMediaProcessID`));


  ALTER TABLE `awsstg`.`speed_bulk_media_process`
ADD INDEX `nextcustompallet_idx` (`NewCustomPalletID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`speed_bulk_media_process`
ADD CONSTRAINT `nextcustompallet`
  FOREIGN KEY (`NewCustomPalletID`)
  REFERENCES `awsstg`.`custompallet` (`CustomPalletID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


  CREATE TABLE `awsstg`.`speed_bulk_media_process_details` (
  `DetailID` INT NOT NULL AUTO_INCREMENT,
  `BulkMediaProcessID` INT NULL DEFAULT NULL,
  `MediaID` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`DetailID`));


  ALTER TABLE `awsstg`.`speed_bulk_media_process_details`
ADD INDEX `bulkmedia_idx` (`BulkMediaProcessID` ASC) VISIBLE,
ADD INDEX `mediaid_idx` (`MediaID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`speed_bulk_media_process_details`
ADD CONSTRAINT `bulkmedia`
  FOREIGN KEY (`BulkMediaProcessID`)
  REFERENCES `awsstg`.`speed_bulk_media_process` (`BulkMediaProcessID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `mediaid`
  FOREIGN KEY (`MediaID`)
  REFERENCES `awsstg`.`speed_media_recovery` (`MediaID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('155', 'Bulk Media Destruction', 'BulkMediaDestruction', 'BulkMediaDestruction', '1', '1', '0', '10', '0');

ALTER TABLE `awsstg`.`speed_bulk_media_process` ADD COLUMN `FacilityID` INT NULL DEFAULT NULL AFTER `Description`;

ALTER TABLE `awsstg`.`speed_bulk_media_process`
ADD COLUMN `ProcessCompledControllerLoginID` VARCHAR(100) NULL DEFAULT NULL AFTER `FacilityID`,
ADD COLUMN `CompletedDate` DATETIME NULL DEFAULT NULL AFTER `ProcessCompledControllerLoginID`,
ADD COLUMN `CompletedBy` INT NULL DEFAULT NULL AFTER `CompletedDate`;

ALTER TABLE `awsstg`.`speed_bulk_media_process` ADD COLUMN `SourceBinID` INT NULL DEFAULT NULL AFTER `CompletedBy`;



ALTER TABLE `awsstg`.`custompallet`
ADD COLUMN `LockedForBulkMediaDestruction` TINYINT NULL DEFAULT 0 AFTER `AcceptAllDisposition`,
ADD COLUMN `LockedTime` DATETIME NULL DEFAULT NULL AFTER `LockedForBulkMediaDestruction`,
ADD COLUMN `LockedBy` INT NULL DEFAULT NULL AFTER `LockedTime`;

CREATE TABLE `awsstg`.`custompallet_lock_history` (
  `LockID` INT NOT NULL AUTO_INCREMENT,
  `CustomPalletID` INT NULL DEFAULT NULL,
  `LockedType` VARCHAR(50) NULL DEFAULT NULL,
  `LockedPage` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`LockID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES
('153', 'Bulk Media Locked Bins', 'BulkDestructionLockedBins', '1', '1', 'eViridis Administration', '0', '63', '0');

ALTER TABLE `awsstg`.`speed_bulk_media_process`
DROP FOREIGN KEY `nextcustompallet`;
ALTER TABLE `awsstg`.`speed_bulk_media_process`
ADD COLUMN `DestructionCustomPalletID` INT NULL DEFAULT NULL COMMENT 'Only used in Degauss and shreded status' AFTER `SourceBinID`,
CHANGE COLUMN `NewCustomPalletID` `NewCustomPalletID` INT NULL DEFAULT NULL COMMENT 'Degauss bin in case of Next status is degauss and shreded\n' ;
ALTER TABLE `awsstg`.`speed_bulk_media_process`
ADD CONSTRAINT `nextcustompallet`
  FOREIGN KEY (`NewCustomPalletID`)
  REFERENCES `awsstg`.`custompallet` (`CustomPalletID`);


After ********
-----------------
ALTER TABLE `awsdev`.`pallets` ADD COLUMN `OnDemandMedia` VARCHAR(5) NULL DEFAULT NULL AFTER `rackIpn`;

-- Add Source Type and Material Type fields to business_rules table
-- In case AWSCustomerID, workflow_id, and part_types don't exist yet, add them first
ALTER TABLE `awsdev`.`business_rule`
ADD COLUMN IF NOT EXISTS `AWSCustomerID` INT NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `workflow_id` INT NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `part_types` VARCHAR(255) NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `idCustomertype` INT NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `MaterialType` VARCHAR(100) NULL DEFAULT NULL;

ALTER TABLE `awsstg`.`disposition`
ADD COLUMN `reuse_hdd_disposition` TINYINT NULL DEFAULT 0 AFTER `destroyed_disposition`,
ADD COLUMN `reuse_ssd_disposition` TINYINT NULL DEFAULT 0 AFTER `reuse_hdd_disposition`;


INSERT INTO `awsstg`.`disposition` (`disposition`, `disposition_description`, `status`, `shipping_removal_type`, `sub_disposition`, `color_code`, `color`, `switch_disposition`, `server_disposition`, `hdd_disposition`, `ssd_disposition`, `destroyed_disposition`, `reuse_hdd_disposition`, `reuse_ssd_disposition`) VALUES ('Terminal-HDDReuse-SC', 'Terminal-HDDReuse-SC', 'Active', '0', '1', '#ff99cc', 'Pink', '0', '0', '0', '0', '0', '1', '0');
INSERT INTO `awsdev`.`sub_disposition_mapping` (`sub_disposition_id`, `parent_disposition_id`) VALUES ('39', '33');  (identify sub_disposition and parent disposition manually, dont copy the query)


INSERT INTO `awsstg`.`disposition` (`disposition`, `disposition_description`, `status`, `shipping_removal_type`, `sub_disposition`, `color_code`, `color`, `switch_disposition`, `server_disposition`, `hdd_disposition`, `ssd_disposition`, `destroyed_disposition`, `reuse_hdd_disposition`, `reuse_ssd_disposition`) VALUES ('Terminal-SSDReuse-SC', 'Terminal-SSDReuse-SC', 'Active', '0', '1', '#ff99cc', 'Pink', '0', '0', '0', '0', '0', '0', '1');
INSERT INTO `awsstg`.`sub_disposition_mapping` (`sub_disposition_id`, `parent_disposition_id`) VALUES ('48', '42'); (identify sub_disposition and parent disposition manually, dont copy the query)

INSERT INTO `awsstg`.`speed_status` (`StatusID`, `Status`, `StatusComments`) VALUES ('4', 'PendingShipping', 'PendingShipping'); (Make sure StatusID = 4)


ALTER TABLE `awsstg`.`custompallet`
ADD COLUMN `MobilityName` TEXT NULL DEFAULT NULL AFTER `LockedBy`,
ADD COLUMN `MobilityNameCreatedBy` INT NULL DEFAULT NULL AFTER `MobilityName`,
ADD COLUMN `MobilityNameCreatedDate` DATETIME NULL DEFAULT NULL AFTER `MobilityNameCreatedBy`;

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Bin Mobility Configuration', 'BinMobilityConfiguration', '', '', '1', '', '1', 'eViridis Administration', '0', '43', '0');

ALTER TABLE `awsstg`.`asset`
ADD COLUMN `RecentWorkflowID` INT NULL DEFAULT NULL AFTER `ActualSerialNumber`,
ADD COLUMN `RecentWorkflowDate` DATETIME NULL DEFAULT NULL AFTER `RecentWorkflowID`,
ADD COLUMN `RecentWorkflowBy` INT NULL DEFAULT NULL AFTER `RecentWorkflowDate`;


CREATE TABLE `awsdev`.`MobilityTracking` (
  `MobilityTrackingID` INT NOT NULL AUTO_INCREMENT,
  `CustomPalletID` INT NULL,
  `oldMobilityName` VARCHAR(100) NULL,
  `NewMobilityName` VARCHAR(100) NULL,
  PRIMARY KEY (`MobilityTrackingID`));

  ALTER TABLE `awsdev`.`shipping_container_serials`
ADD COLUMN `MediaID` INT NULL DEFAULT NULL AFTER `byproduct_id`,
ADD COLUMN `MediaSerialNumber` VARCHAR(50) NULL DEFAULT NULL AFTER `MediaID`;

INSERT INTO `awsdev`.`speed_status` (`StatusID`, `Status`, `StatusComments`) VALUES ('5', 'Added to Shipment', 'Added to Shipment');
INSERT INTO `awsdev`.`speed_status` (`StatusID`, `Status`, `StatusComments`) VALUES ('6', 'Shipped', 'Shipped'); (Make sure it is 5 and 6)

ALTER TABLE `awsdev`.`speed_media_recovery`
ADD COLUMN `ShippingContainerID` VARCHAR(50) NULL DEFAULT NULL AFTER `origin_bin_id`,
ADD COLUMN `DateAddedToShipmentContainer` DATETIME NULL DEFAULT NULL AFTER `ShippingContainerID`;


ALTER TABLE `awsdev`.`asset`
ADD COLUMN `ShippingContainerID` VARCHAR(50) NULL DEFAULT NULL AFTER `RecentWorkflowBy`,
ADD COLUMN `ShippingContainerAddedDate` DATETIME NULL DEFAULT NULL AFTER `ShippingContainerID`,
ADD COLUMN `ShippingContainerAddedBy` INT NULL DEFAULT NULL AFTER `ShippingContainerAddedDate`;


ALTER TABLE `awsstg`.`inventory`
ADD COLUMN `ShippingContainerID` VARCHAR(50) NULL DEFAULT NULL AFTER `ActualSerialNumber`,
ADD COLUMN `ShippingContainerAddedDate` DATETIME NULL DEFAULT NULL AFTER `ShippingContainerID`,
ADD COLUMN `ShippingContainerAddedBy` INT NULL DEFAULT NULL AFTER `ShippingContainerAddedDate`;

ALTER TABLE `awsdev`.`speed_server_recovery`
ADD COLUMN `ShippingContainerID` VARCHAR(50) NULL DEFAULT NULL AFTER `origin_disposition_id`,
ADD COLUMN `ShippingContainerAddedDate` DATETIME NULL DEFAULT NULL AFTER `ShippingContainerID`,
ADD COLUMN `ShippingContainerAddedBy` INT NULL DEFAULT NULL AFTER `ShippingContainerAddedDate`;


ALTER TABLE `awsstg`.`speed_server_recovery`
ADD COLUMN `ShippingID` VARCHAR(50) NULL DEFAULT NULL AFTER `ShippingContainerAddedBy`,
ADD COLUMN `ShippedDate` DATE NULL DEFAULT NULL AFTER `ShippingID`,
ADD COLUMN `ShippedTime` TIME NULL DEFAULT NULL AFTER `ShippedDate`;

ALTER TABLE `awsdev`.`speed_media_recovery`
ADD COLUMN `ShippingID` VARCHAR(50) NULL DEFAULT NULL AFTER `DateAddedToShipmentContainer`,
ADD COLUMN `ShippedDate` DATE NULL DEFAULT NULL AFTER `ShippingID`,
ADD COLUMN `ShippedTime` TIME NULL DEFAULT NULL AFTER `ShippedDate`;

MPN Changes START
-----------------

ALTER TABLE `awsstg`.`catlog_creation`
ADD COLUMN `capability` TEXT NULL DEFAULT NULL AFTER `priority_type`,
ADD COLUMN `procedure_id` TEXT NULL DEFAULT NULL AFTER `capability`,
ADD COLUMN `FacilityID` INT NULL DEFAULT NULL AFTER `procedure_id`,
ADD COLUMN `BRE_sort_quantity` INT NULL DEFAULT NULL AFTER `FacilityID`;


ALTER TABLE `awsdev`.`mpn_history`
ADD COLUMN `capability` TEXT NULL DEFAULT NULL AFTER `priority_type`,
ADD COLUMN `procedure_id` TEXT NULL DEFAULT NULL AFTER `capability`,
ADD COLUMN `FacilityID` INT NULL DEFAULT NULL AFTER `procedure_id`,
ADD COLUMN `BRE_sort_quantity` INT NULL DEFAULT NULL AFTER `FacilityID`;


ALTER TABLE `awsstg`.`catlog_creation`
ADD INDEX `FK_facility_idx` (`FacilityID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`catlog_creation`
ADD CONSTRAINT `FK_facility`
  FOREIGN KEY (`FacilityID`)
  REFERENCES `awsstg`.`facility` (`FacilityID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('1', 'Yes', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('1', 'No', 'Active');


INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_id`, `attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('27', 'Capability', 'CatalogMPN', 'CatalogMPN', 'capability', 'Active');
INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_id`, `attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('28', 'Procedure ID', 'CatalogMPN', 'CatalogMPN', 'procedure_id', 'Active');
INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_id`, `attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('29', 'BRE Sort Quantity', 'CatalogMPN', 'CatalogMPN', 'BRE_sort_quantity', 'Active');
Make sure ids are 27,28,29


INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Sanitization-Spare', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Sanitization-Liquidation', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Sanitization-Recycle', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Sanitization-RMA', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Failure_Analysis-FullTest', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Failure_Analysis-LightTest', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Repair-FRUReplacement', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Repair-HeatsinkReplacement', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Extract-FRUForRepair', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Extract-HeatSinkForRepair', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'RMA-Standard', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'RMA-Virtual', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('27', 'Liquidation-Active', 'Active');

INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_id`, `attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('30', 'Daily Extracted Quantity', 'Internal', 'Internal', 'daily_extracted_quantity', 'Active') (Make sure id is 30)
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('30', 'BRE_sort_quantity', 'Active');

insert into awsdev.business_rule_attribute_values (attribute_id,value,status)
select 28,`value`,'Active' from awsdev.business_rule_attribute_values where attribute_id = 27;

CREATE TABLE `awsstg`.`daily_received_summary` (
  `ReceiveID` INT NOT NULL AUTO_INCREMENT,
  `mpn_id` VARCHAR(100) NULL DEFAULT NULL,
  `apn_id` VARCHAR(100) NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  `ReceivedCount` INT NULL DEFAULT NULL,
  `ReceivedDate` DATE NULL DEFAULT NULL,
  PRIMARY KEY (`ReceiveID`));

ALTER TABLE `awsstg`.`daily_received_summary`
ADD INDEX `dailyreceived_facility_idx` (`FacilityID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`daily_received_summary`
ADD CONSTRAINT `dailyreceived_facility`
  FOREIGN KEY (`FacilityID`)
  REFERENCES `awsstg`.`facility` (`FacilityID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


INSERT INTO `awsdev`.`tabs` (`TabName`, `TabLink`, `Description`, `AccountID`, `Status`, `Dashboard`, `Order`, `TabIcon`) VALUES ('Dashboard', 'dashboard', 'dashboard', '1', '1', '0', '16', 'dashboard');

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `CustomFields`, `Order`) VALUES ('164', 'Home', 'Home', 'Home', '', '1', '', '1', '0', '1'); (Tab ID column should be tab id from tabs table for dashboard)


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Daily Extracted Records', 'DailyExtractedRecords', '1', '1', 'eViridis Administration', '0', '69', '0');

CREATE TABLE `awsdev`.`region` (
  `Region` INT NOT NULL AUTO_INCREMENT,
  `RegionName` VARCHAR(100) NULL DEFAULT NULL,
  `Description` TEXT NULL DEFAULT NULL,
  PRIMARY KEY (`Region`));


INSERT INTO `awsstg`.`region` (`Region`, `RegionName`, `Description`) VALUES ('1', 'US', 'Unites States of America');
INSERT INTO `awsstg`.`region` (`Region`, `RegionName`, `Description`) VALUES ('2', 'EMEA', 'Middle East');
INSERT INTO `awsstg`.`region` (`Region`, `RegionName`, `Description`) VALUES ('3', 'ASIA', 'ASIA');

ALTER TABLE `awsstg`.`facility`
ADD COLUMN `Region` INT NULL DEFAULT NULL AFTER `Currency`;

ALTER TABLE `awsstg`.`facility`
ADD INDEX `facilitytoregion_idx` (`Region` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`facility`
ADD CONSTRAINT `facilitytoregion`
  FOREIGN KEY (`Region`)
  REFERENCES `awsstg`.`region` (`Region`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;



  CREATE TABLE awsdev.mpn_file_uploads_details (
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  mpn_id VARCHAR(255),
  apn_id VARCHAR(255),
  part_type VARCHAR(255),
  manufacturer_id VARCHAR(255),
  part_description VARCHAR(255),
  sanitization_flag VARCHAR(255),
  capability VARCHAR(255),
  procedure_id INT,
  ext_mpn_id VARCHAR(255),
  cooid VARCHAR(255),
  facility_id VARCHAR(255),
  region VARCHAR(255),
  demand_type VARCHAR(255),
  bre_sort_quantity INT,
  ww_eligibility_flag VARCHAR(255),
  ww_priority_type VARCHAR(255)
);

ALTER TABLE `awsdev`.`mpn_file_uploads_details`
CHANGE COLUMN `procedure_id` `procedure_id` TEXT NULL DEFAULT NULL ;

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES
 ('155', 'Pending Sanitization', 'PendingSanitization', 'PendingSanitization', '1', '1', '0', '2', '0');

 ALTER TABLE `awsstg`.`asset`
ADD COLUMN `RecentDispositionDate` DATETIME NULL DEFAULT NULL AFTER `ShippingContainerAddedBy`,
ADD COLUMN `RecentDispositionRuleID` INT NULL DEFAULT NULL AFTER `RecentDispositionDate`,
ADD COLUMN `RecentDispositionBy` INT NULL DEFAULT NULL AFTER `RecentDispositionRuleID`,
ADD COLUMN `RecentDispositionComments` TEXT NULL DEFAULT NULL AFTER `RecentDispositionBy`;


ALTER TABLE `awsstg`.`mpn_file_uploads` ADD COLUMN `Completed` TINYINT NULL DEFAULT 0 AFTER `Comments`;
ALTER TABLE `awsstg`.`mpn_file_uploads_details` ADD COLUMN `UploadID` INT NULL DEFAULT NULL AFTER `ww_priority_type`;

ALTER TABLE `awsstg`.`mpn_file_uploads_details`
ADD INDEX `detailstoupload_idx` (`UploadID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`mpn_file_uploads_details`
ADD CONSTRAINT `detailstoupload`
  FOREIGN KEY (`UploadID`)
  REFERENCES `awsstg`.`mpn_file_uploads` (`UploadID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

  ALTER TABLE `awsstg`.`mpn_file_uploads_details` ADD COLUMN `Completed` TINYINT NULL DEFAULT 0 AFTER `UploadID`;

  ALTER TABLE `awsstg`.`mpn_file_uploads_details` ADD COLUMN `Comments` TEXT NULL DEFAULT NULL AFTER `Completed`;

  ALTER TABLE `awsstg`.`mpn_file_uploads_details`
ADD COLUMN `FacilityID` INT NULL DEFAULT NULL AFTER `Comments`,
ADD COLUMN `idManufacturer` INT NULL DEFAULT NULL AFTER `FacilityID`;


ALTER TABLE `awsstg`.`mpn_file_uploads_details`
ADD INDEX `facilityname` (`facility_id` ASC) INVISIBLE,
ADD INDEX `manufacturername` (`manufacturer_id` ASC) VISIBLE;
;


ALTER TABLE `awsstg`.`catlog_creation`
ADD COLUMN `id` INT NULL DEFAULT NULL COMMENT 'id from mpn_file_uploads_details' AFTER `BRE_sort_quantity`,
ADD COLUMN `FacilityName` VARCHAR(250) NULL DEFAULT NULL COMMENT 'Facility Name from mpn_file_uploads_details' AFTER `id`,
ADD COLUMN `ManufactureName` VARCHAR(250) NULL DEFAULT NULL COMMENT 'ManufactureName from mpn_file_uploads_details' AFTER `FacilityName`;


Copy Stored Procedure RenameAndCreateTableWithSameColumns from Staging to Production


ALTER TABLE `awsstg`.`catlog_creation`
ADD COLUMN `UploadID` INT NULL DEFAULT NULL AFTER `ManufactureName`;


ALTER TABLE `awsdev`.`mpn_file_uploads` ADD COLUMN `UploadType` VARCHAR(100) NULL DEFAULT NULL AFTER `Completed`;

CREATE TABLE awsstg.`mpnscript_upload` (
  `mpn_id` varchar(255) DEFAULT NULL,
  `apn_id` varchar(255) DEFAULT NULL,
  `part_type` varchar(255) DEFAULT NULL,
  `manufacturer_id` varchar(255) DEFAULT NULL,
  `part_description` varchar(255) DEFAULT NULL,
  `sanitization_flag` varchar(255) DEFAULT NULL,
  `capability` varchar(255) DEFAULT NULL,
  `procedure_id` int DEFAULT NULL,
  `ext_mpn_id` varchar(255) DEFAULT NULL,
  `cooid` varchar(255) DEFAULT NULL,
  `facility_id` varchar(255) DEFAULT NULL,
  `region` varchar(255) DEFAULT NULL,
  `demand_type` varchar(255) DEFAULT NULL,
  `bre_sort_quantity` int DEFAULT NULL,
  `ww_eligibility_flag` varchar(255) DEFAULT NULL,
  `ww_priority_type` varchar(255) DEFAULT NULL,
  `FacilityID` int DEFAULT NULL,
  `idManufacturer` int DEFAULT NULL,
  `UploadID` int DEFAULT NULL,
  KEY `facilityindex` (`facility_id`) /*!80000 INVISIBLE */,
  KEY `manuindex` (`manufacturer_id`)
)

ALTER TABLE `awsstg`.`mpnscript_upload`
CHANGE COLUMN `procedure_id` `procedure_id` VARCHAR(255) NULL DEFAULT NULL ;

ALTER TABLE `awsdev`.`mpn_file_uploads` ADD COLUMN `RenamedTableName` VARCHAR(250) NULL DEFAULT NULL AFTER `UploadType`;

copy folder administration/vendor directly from stg to production, this is not in GET


ALTER TABLE `awsstg`.`mpnscript_upload` CHANGE COLUMN `procedure_id` `procedure_id` TEXT NULL DEFAULT NULL ;

Need some db changes


After MPN Release
------------------

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('158', 'Pending Outbound Containers', 'PendingOutboundShipments', 'Pending Outbound Shipments', '1', '', '1', '', '0', '13', '0');
ALTER TABLE `awsstg`.`shipping_containers` ADD COLUMN `ShippingNotes` VARCHAR(200) NULL DEFAULT NULL AFTER `RecentSealBy`;

ALTER TABLE `awsstg`.`shipping_containers` ADD COLUMN `PartTypeSummary` TEXT NULL DEFAULT NULL AFTER `ShippingNotes`;

UPDATE `awsstg`.`business_rule_attributes` SET `attribute_name` = 'Demand Flag' WHERE (`attribute_id` = '1');

UPDATE `awsstg`.`business_rule_attributes` SET `status` = 'Inactive' WHERE (`attribute_id` = '2');
UPDATE `awsstg`.`business_rule_attributes` SET `status` = 'Inactive' WHERE (`attribute_id` = '9');
UPDATE `awsstg`.`business_rule_attributes` SET `status` = 'Inactive' WHERE (`attribute_id` = '8');
UPDATE `awsstg`.`business_rule_attributes` SET `status` = 'Inactive' WHERE (`attribute_id` = '7');
UPDATE `awsstg`.`business_rule_attributes` SET `status` = 'Inactive' WHERE (`attribute_id` = '5');
UPDATE `awsstg`.`business_rule_attributes` SET `status` = 'Inactive' WHERE (`attribute_id` = '22');

Delete from  awsstg.business_rule_attribute_values where attribute_id = 1 and `value` not in ('Yes','No');

update awsstg.shipping_containers c,awsstg.shipping s set c.disposition_id = s.disposition_id where c.ShippingID = s.ShippingID and isnull(c.disposition_id);

ALTER TABLE `awsstg`.`shipping_containers` ADD COLUMN `RemovalCode` VARCHAR(50) NULL DEFAULT NULL AFTER `PartTypeSummary`;

ALTER TABLE `awsstg`.`shipping_containers` ADD COLUMN `QuarantineToActiveAuditController` VARCHAR(100) NULL DEFAULT NULL AFTER `RemovalCode`;

ALTER TABLE `awsstg`.`speed_server_recovery` ADD COLUMN `MediaRecovery_VerificationID` VARCHAR(50) NULL DEFAULT NULL AFTER `ShippedTime`;

update awsstg.speed_server_recovery s,awsstg.speed_media_recovery m set s.MediaRecovery_VerificationID = m.VerificationID where s.StatusID = 1 and s.ServerSerialNumber = m.ServerSerialNumber;
//this is for copying verification id from media recovery to server recovery

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Bin Audit Controls', 'BinAuditControls', '1', '1', 'eViridis Administration', '0', '70', '0');

CREATE TABLE `awsstg`.`bin_audit_controls` (
  `ControlID` INT NOT NULL AUTO_INCREMENT,
  `Type` VARCHAR(50) NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  `disposition_id` INT NULL DEFAULT NULL,
  `BinsCount` INT NULL DEFAULT NULL,
  `AuditFrequency` VARCHAR(50) NULL DEFAULT NULL,
  `Accuracy` INT NULL DEFAULT NULL,
  `Status` VARCHAR(10) NULL DEFAULT NULL,
  `AssignedBinName` VARCHAR(100) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ControlID`));

//BIN Audit Controls DB Changes START (Not included in release db changes)

  ALTER TABLE `awsstg`.`bin_audit_controls`
ADD INDEX `facility_index_idx` (`FacilityID` ASC) VISIBLE,
ADD INDEX `disposition_index_idx` (`disposition_id` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`bin_audit_controls`
ADD CONSTRAINT `facility_index`
  FOREIGN KEY (`FacilityID`)
  REFERENCES `awsstg`.`facility` (`FacilityID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `disposition_index`
  FOREIGN KEY (`disposition_id`)
  REFERENCES `awsstg`.`disposition` (`disposition_id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

  ALTER TABLE `awsstg`.`bin_audit_controls` ADD COLUMN `CustomPalletID` INT NULL DEFAULT NULL AFTER `UpdatedBy`;

  ALTER TABLE `awsstg`.`bin_audit_controls` ADD COLUMN `LastRanDate` DATETIME NULL DEFAULT NULL AFTER `CustomPalletID`;

  ALTER TABLE `awsstg`.`custompallet`
ADD COLUMN `ControlID` INT NULL DEFAULT NULL COMMENT 'bin_audit_control id' AFTER `MobilityNameCreatedDate`,
ADD COLUMN `DateAddedToBinAudit` DATETIME NULL DEFAULT NULL AFTER `ControlID`,
ADD COLUMN `AuditDate` DATETIME NULL DEFAULT NULL AFTER `DateAddedToBinAudit`,
ADD COLUMN `AuditLocked` TINYINT NULL DEFAULT 0 AFTER `AuditDate`;

CREATE TABLE `awsstg`.`bin_audit_bins` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `ControlID` INT NULL DEFAULT NULL,
  `CustomPalletID` INT NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  `AuditLocked` TINYINT NULL DEFAULT 1 COMMENT 'If added to bin audit, it will be locked by default',
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `AuditedDate` DATETIME NULL DEFAULT NULL,
  `AuditedBy` INT NULL DEFAULT NULL,
  `Status` VARCHAR(50) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Unlock Bin Audit', 'BinAuditLockedBins', 'BinAuditLockedBins', '', '1', '', '1', 'eViridis Administration', '0', '71', '0');

  ALTER TABLE `awsstg`.`bin_audit_bins` ADD COLUMN `UnlockedDate` DATETIME NULL DEFAULT NULL AFTER `Status`,ADD COLUMN `UnlockedBy` INT NULL DEFAULT NULL AFTER `UnlockedDate`;


  ALTER TABLE `awsstg`.`bin_audit_bins` ADD COLUMN `Type` VARCHAR(10) NULL DEFAULT 'Schedule' AFTER `UnlockedBy`;

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('162', 'Pending Bin Audit', 'PendingBinAudit', 'PendingBinAudit', '', '1', '', '1', '', '0', '3', '0');

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('162', 'Bin Audit', 'BinAudit', 'BinAudit', '', '1', '', '1', '', '0', '4', '0');


  CREATE TABLE `awsstg`.`bin_audit_records` (
  `AuditRecordID` INT NOT NULL AUTO_INCREMENT,
  `BinAuditID` INT NULL DEFAULT NULL,
  `ControlID` INT NULL DEFAULT NULL,
  `SourceBinID` INT NULL DEFAULT NULL,
  `SourceBinName` TEXT NULL DEFAULT NULL,
  `SourceDisposition` INT NULL DEFAULT NULL,
  `SameDispositionBinID` INT NULL DEFAULT NULL,
  `SameDispositionBinName` TEXT NULL DEFAULT NULL,
  `AllDispositionBinID` INT NULL DEFAULT NULL,
  `AllDispositionBinName` TEXT NULL DEFAULT NULL,
  `SerialNumber` VARCHAR(100) NULL DEFAULT NULL,
  `MPN` VARCHAR(100) NULL DEFAULT NULL,
  `EvaluationResult` VARCHAR(200) NULL DEFAULT NULL,
  `NewBinID` INT NULL DEFAULT NULL,
  `NewBinName` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `AssetScanID` BIGINT NULL DEFAULT NULL,
  PRIMARY KEY (`AuditRecordID`));

  ALTER TABLE `awsstg`.`bin_audit_records`
CHANGE COLUMN `BinAuditID` `BinAuditID` INT NULL DEFAULT NULL COMMENT 'custom pallet record from bin_audit_bins table' ,
ADD INDEX `binauditbinindex_idx` (`BinAuditID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`bin_audit_records`
ADD CONSTRAINT `binauditbinindex`
  FOREIGN KEY (`BinAuditID`)
  REFERENCES `awsstg`.`bin_audit_bins` (`ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `awsstg`.`bin_audit_records`
ADD COLUMN `NewDispositionID` INT NULL DEFAULT NULL AFTER `AssetScanID`,
ADD COLUMN `NewDispositionName` TEXT NULL DEFAULT NULL AFTER `NewDispositionID`;

ALTER TABLE `awsstg`.`bin_audit_controls` ADD COLUMN `part_type` TEXT NULL DEFAULT NULL AFTER `LastRanDate`;


ALTER TABLE `awsstg`.`bin_audit_bins`
ADD COLUMN `TotalAssets` INT NULL DEFAULT NULL AFTER `Type`,
ADD COLUMN `Accuracy` FLOAT NULL DEFAULT 0 AFTER `TotalAssets`;

ALTER TABLE `awsstg`.`bin_audit_bins` ADD COLUMN `AccuracyTarget` FLOAT NULL DEFAULT NULL AFTER `Accuracy`;

ALTER TABLE `awsstg`.`bin_audit_bins` ADD COLUMN `SuccessfulAssets` INT NULL DEFAULT 0 AFTER `TotalAssets`;

ALTER TABLE `awsstg`.`bin_audit_records` ADD COLUMN `AuditNotes` TEXT NULL DEFAULT NULL AFTER `NewDispositionName`;

INSERT INTO `awsstg`.`custompallet_status` (`StatusID`, `Status`, `StatusDescription`) VALUES ('4', 'AuditLocked', 'AuditLocked');

ALTER TABLE `awsstg`.`Rig` ADD COLUMN `RigLimit` INT NULL DEFAULT NULL AFTER `Status`;

update awsstg.Rig set RigLimit = 10 where 1; (Updating rig limit to 10 for all available rigs by default)


ALTER TABLE `awsstg`.`bin_audit_records` ADD COLUMN `Damaged` VARCHAR(50) NULL DEFAULT NULL AFTER `AuditNotes`;

CREATE TABLE `awsstg`.`bin_audit_evaluation_results_combinations` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `SerialNumberStatus` VARCHAR(100) NULL DEFAULT NULL,
  `SerialInBinStatus` VARCHAR(100) NULL DEFAULT NULL,
  `MPNStatus` VARCHAR(100) NULL DEFAULT NULL,
  `DamageStatus` VARCHAR(100) NULL DEFAULT NULL,
  `DispositionType` VARCHAR(50) NULL DEFAULT NULL,
  `EvaluationResult` VARCHAR(200) NULL DEFAULT NULL,
  `Status` VARCHAR(10) NULL DEFAULT 'Active',
  PRIMARY KEY (`ID`));



  INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Not Active', 'In Bin', 'Matches', 'No Damage', 'Match', 'Fail-InActiveSerial', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Not Active', 'Not In Bin', 'Matches', 'No Damage', 'Match', 'Fail-InActiveSerial', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Not Active', 'In Bin', 'Matches', 'No Damage', 'Do not match', 'Fail-InActiveSerial', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Not Active', 'Not In Bin', 'Matches', 'No Damage', 'Do not match', 'Fail-InActiveSerial', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'Not in Bin Physically', 'Matches', 'No Damage', 'Match', 'Fail-VirtualSerialBinMismatch', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'Not In Bin', 'Matches', 'No Damage', 'Match', 'Fail-SerialBinMismatch', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'Not In Bin', 'Matches', 'No Damage', 'Do not match', 'Fail-SerialBinMismatch', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'In Bin', 'Does not Matches', 'No Damage', 'Match', 'Fail-MpnMismatch', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'In Bin', 'Does not Matches', 'No Damage', 'Do not match', 'Fail-MpnMismatch', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'Not In Bin', 'Does not Matches', 'No Damage', 'Match', 'Fail-SerialMpnMismatch', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'Not In Bin', 'Does not Matches', 'No Damage', 'Do not match', 'Fail-SerialMpnMismatch', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'In Bin', 'Matches', 'Non-Cosmetic Damage', 'Match', 'Fail-Damage', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'In Bin', 'Matches', 'Non-Cosmetic Damage', 'Do not match', 'Fail-Damage', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'In Bin', 'Matches', 'No Damage', 'Match', 'Pass-Audit', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'In Bin', 'Matches', 'Cosmetic Damage (rusty parts/brackets)', 'Match', 'Fail-Other', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'In Bin', 'Matches', 'Cosmetic Damage (rusty parts/brackets)', 'Do not match', 'Fail-Other', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'In Bin', 'Matches', 'No Damage', 'Do not match', 'Fail-DispositionMismatch', 'Active');
INSERT INTO `awsstg`.`bin_audit_evaluation_results_combinations` (`SerialNumberStatus`, `SerialInBinStatus`, `MPNStatus`, `DamageStatus`, `DispositionType`, `EvaluationResult`, `Status`) VALUES ('Active', 'Not In Bin', 'Does not Matches', 'Non-Cosmetic Damage', 'Do not match', 'Fail-AllAudit', 'Active');


ALTER TABLE `awsstg`.`bin_audit_evaluation_results_combinations` ADD COLUMN `DestinationBinType` VARCHAR(50) NULL DEFAULT NULL AFTER `Status`;



UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '3');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '4');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '5');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '7');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '9');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '10');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '11');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '12');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '13');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '15');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '16');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '17');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'All' WHERE (`ID` = '18');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'Same' WHERE (`ID` = '1');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'Same' WHERE (`ID` = '2');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'Same' WHERE (`ID` = '6');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'Same' WHERE (`ID` = '8');
UPDATE `awsstg`.`bin_audit_evaluation_results_combinations` SET `DestinationBinType` = 'Same' WHERE (`ID` = '14');



ALTER TABLE `awsstg`.`shipping`
ADD COLUMN `DemanASNGeneratedDate` DATETIME NULL DEFAULT NULL AFTER `ShipmentProcessedBy`,
ADD COLUMN `DemanASNGeneratedBy` INT NULL DEFAULT NULL AFTER `DemanASNGeneratedDate`,
ADD COLUMN `DemanASNGenerated` TINYINT NULL DEFAULT 0 AFTER `DemanASNGeneratedBy`;

//IN DEMAN
CREATE TABLE demanufacturingstg.aws_shipments (
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  origin_ticket_id VARCHAR(255),
  container_id VARCHAR(255),
  container_type VARCHAR(255),
  seal1 VARCHAR(255),
  seal2 VARCHAR(255),
  seal3 VARCHAR(255),
  seal4 VARCHAR(255),
  weight_value VARCHAR(255),
  container_apn_id VARCHAR(255),
  serial_id VARCHAR(255),
  mpn_id VARCHAR(255),
  material_type VARCHAR(255)
);

ALTER TABLE `demanufacturingstg`.`aws_shipments`
ADD COLUMN `CreatedDate` DATETIME NULL DEFAULT NULL AFTER `material_type`;

ALTER TABLE `demanufacturingstg`.`aws_shipments`
ADD COLUMN `origin_location_id` VARCHAR(100) NULL DEFAULT NULL AFTER `CreatedDate`;

ALTER TABLE `demanufacturingstg`.`aws_shipments`
ADD COLUMN `exception_details` TEXT NULL DEFAULT NULL AFTER `origin_location_id`;

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Bin To Container Consolidation', 'BinContainerConsolidation', '', '', '1', '', '1', 'eViridis Administration', '0', '59', '0');


ALTER TABLE `awsstg`.`bin_consolidation_asset_movement`
ADD COLUMN `MovementFrom` VARCHAR(100) NULL DEFAULT NULL AFTER `MediaID`,
ADD COLUMN `MovementTo` VARCHAR(100) NULL DEFAULT NULL AFTER `MovementFrom`,
ADD COLUMN `FromShippingContainerID` VARCHAR(100) NULL DEFAULT NULL AFTER `MovementTo`,
ADD COLUMN `ToShippingContainerID` VARCHAR(100) NULL DEFAULT NULL AFTER `FromShippingContainerID`;

ALTER TABLE `awsstg`.`bin_consolidation_asset_movement`
ADD COLUMN `InventoryID` INT NULL DEFAULT NULL AFTER `ToShippingContainerID`;


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Location Group', 'LocationGroupList', '', '', '1', '', '1', 'Warehouse Management', '0', '23', '0');

ALTER TABLE `awsstg`.`speed_rackdetails_api` ADD COLUMN `workType` VARCHAR(100) NULL DEFAULT NULL AFTER `site`;


ALTER TABLE `awsstg`.`pallets` ADD COLUMN `workType` VARCHAR(100) NULL DEFAULT NULL AFTER `OnDemandMedia`;


update awsdev.lables set status = "N"  where 1;

insert into awsdev.lables (lable_name,status,accountID) values
('WIP-Workstation','Y','1'),
('WIP-Storage','Y','1'),
('WIP-Staging','Y','1'),
('WIP-Quarantine','Y','1'),
('Inbound-Staging','Y','1'),
('Inbound-Storage','Y','1'),
('Inbound-Quarantine','Y','1'),
('Outbound-Quarantine','Y','1'),
('Outbound-Staging','Y','1'),
('Outbound-Storage','Y','1'),
('WIP-Overflow','Y','1'),
('Inbound-Overflow','Y','1'),
('Outbound-Overflow','Y','1')


INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Location Label', 'LableList', '', '', '1', '', '1', 'Warehouse Management', '0', '54', '0');
update awsdev.lables set accountID = 0 where status = 'N';
ALTER TABLE `awsstg`.`lables` ADD COLUMN `LocationType` VARCHAR(100) NULL DEFAULT NULL AFTER `color`;

CREATE TABLE `awsstg`.`bin_types` (
  `BinTypeID` INT NOT NULL AUTO_INCREMENT,
  `BinType` VARCHAR(100) NULL DEFAULT NULL,
  `BinTypeDescription` TEXT NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  `Status` VARCHAR(50) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`BinTypeID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Bin Type', 'BinTypeList', '', '', '1', '', '1', 'eViridis Administration', '0', '43', '0');


ALTER TABLE `awsstg`.`location_group` ADD COLUMN `BinTypeID` INT NULL DEFAULT NULL AFTER `ProcessLocation`;
ALTER TABLE `awsstg`.`location` ADD COLUMN `BinTypeID` INT NULL DEFAULT NULL AFTER `ProcessLocation`;
ALTER TABLE `awsstg`.`custompallet` ADD COLUMN `BinTypeID` INT NULL DEFAULT NULL AFTER `AuditLocked`;


ALTER TABLE `awsstg`.`custompallet`
ADD COLUMN `PartTypeSummary` TEXT NULL DEFAULT NULL AFTER `BinTypeID`,
ADD COLUMN `PartTypeSummaryUpdatedDate` DATETIME NULL DEFAULT NULL AFTER `PartTypeSummary`,
ADD COLUMN `PartTypeSummaryUpdatedBy` INT NULL DEFAULT NULL AFTER `PartTypeSummaryUpdatedDate`;

ALTER TABLE `awsstg`.`site` ADD COLUMN `GroupID` INT NULL DEFAULT NULL AFTER `workflow_id`;

ALTER TABLE `awsstg`.`speed_server_recovery` ADD COLUMN `MediaRecovery_AuditControllerID` VARCHAR(100) NULL DEFAULT NULL AFTER `MediaRecoveredBy`;

ALTER TABLE `awsdev`.`facility` ADD COLUMN `WasteClassification` TINYINT NULL DEFAULT 0 AFTER `Region`;

ALTER TABLE `awsdev`.`package` ADD COLUMN `WasteClassificationType` VARCHAR(10) NULL DEFAULT NULL AFTER `OptionalLocation`;


CREATE TABLE `awsdev`.`waste_codes` (
  `WasteCodeID` INT NOT NULL AUTO_INCREMENT,
  `WasteCode` VARCHAR(50) NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  `disposition_id` INT NULL DEFAULT NULL,
  `part_type` VARCHAR(50) NULL DEFAULT NULL,
  `Description` TEXT NULL DEFAULT NULL,
  `StatusID` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`WasteCodeID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Waste Code', 'WasteCodeList', '', '', '1', '', '1', 'eViridis Administration', '0', '68', '0');

  ALTER TABLE `awsstg`.`pallets` ADD COLUMN `WasteCustomerID` VARCHAR(100) NULL DEFAULT NULL AFTER `workType`,ADD COLUMN `WasteClassificationType` VARCHAR(10) NULL DEFAULT NULL AFTER `WasteCustomerID`;

  ALTER TABLE `awsstg`.`waste_codes` ADD COLUMN `WasteClassificationType` VARCHAR(10) NULL DEFAULT NULL AFTER `UpdatedBy`;

  INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_id`, `attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('31', 'Waste Classification Type', 'Internal', 'Internal', 'WasteClassificationType', 'Active'); (make sure attribute id is 31)

  INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('31', 'UEEE', 'Active');
  INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('31', 'WEEE', 'Active');

  ALTER TABLE `awsstg`.`disposition` ADD COLUMN `WasteClassificationType` VARCHAR(10) NULL DEFAULT NULL AFTER `eligible_for_bin_to_container`;

  from left_menus rename Waste Code to Classification Code


  ALTER TABLE `awsstg`.`by_products` ADD COLUMN `WasteClassificationType` VARCHAR(10) NULL DEFAULT NULL AFTER `UpdatedBy`;

  from left_menus rename Byproduct to Unserialized Shipping

After Release
--------------

  ALTER TABLE `awsstg`.`disposition` ADD COLUMN `eligible_for_bin_consolidation` TINYINT NULL DEFAULT 0 AFTER `WasteClassificationType`;

  CREATE TABLE `awsstg`.`disposition_override_reason` (
  `ReasonID` INT NOT NULL AUTO_INCREMENT,
  `OverrideReason` TEXT NULL DEFAULT NULL,
  `OverrideDescription` TEXT NULL DEFAULT NULL,
  `NotesRequired` TINYINT NULL DEFAULT 0,
  `Status` VARCHAR(10) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ReasonID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Disposition Override Reason', 'DispositionOverrideReasonList', 'DispositionOverrideReasonList', '', '1', '', '1', 'eViridis Administration', '0', '63', '0');


CREATE TABLE `awsstg`.`disposition_override_eligibility` (
  `EligibilityID` INT NOT NULL AUTO_INCREMENT,
  `FacilityID` INT NULL DEFAULT NULL,
  `DispositionFrom` INT NULL DEFAULT NULL,
  `DispositionTo` INT NULL DEFAULT NULL,
  `EligibilityType` VARCHAR(100) NULL DEFAULT NULL,
  `Status` VARCHAR(10) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`EligibilityID`));


  ALTER TABLE `awsstg`.`disposition_override_eligibility`
ADD INDEX `facilityindex_idx` (`FacilityID` ASC) VISIBLE,
ADD INDEX `dispositionfromindex_idx` (`DispositionFrom` ASC) VISIBLE,
ADD INDEX `dispositiontoindex_idx` (`DispositionTo` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`disposition_override_eligibility`
ADD CONSTRAINT `facilityindex`
  FOREIGN KEY (`FacilityID`)
  REFERENCES `awsstg`.`facility` (`FacilityID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `dispositionfromindex`
  FOREIGN KEY (`DispositionFrom`)
  REFERENCES `awsstg`.`disposition` (`disposition_id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `dispositiontoindex`
  FOREIGN KEY (`DispositionTo`)
  REFERENCES `awsstg`.`disposition` (`disposition_id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Disposition Override Eligibility', 'DispositionOverrideEligibilityList', 'DispositionOverrideEligibilityList', '', '1', '', '1', 'eViridis Administration', '0', '64', '0');

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Disposition Override Assets', 'DispositionOverrideAssets', 'DispositionOverrideAssets', '', '1', '', '1', 'eViridis Administration', '0', '65', '0');
  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Disposition Override Bulk', 'DispositionOverrideBulk', 'DispositionOverrideBulk', '', '1', '', '1', 'eViridis Administration', '0', '66', '0');
  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Disposition Override Container', 'DispositionOverrideContainer', 'DispositionOverrideContainer', '', '1', '', '1', 'eViridis Administration', '0', '67', '0');

Change group name from "eViridis Administration" to "Disposition Override" (new group in administration and order from 1,2,3 )
same thing for Disposition Override Eligibility and Disposition Override Reason


ALTER TABLE `awsstg`.`disposition_override_assets`
ADD COLUMN `Notes` TEXT NULL DEFAULT NULL AFTER `RequesterLogin`,
ADD COLUMN `TPVRController` VARCHAR(100) NULL DEFAULT NULL AFTER `Notes`,
ADD COLUMN `OverrideType` VARCHAR(100) NULL DEFAULT NULL AFTER `TPVRController`;


CREATE TABLE `awsstg`.`disposition_override` (
  `OverrideID` INT NOT NULL AUTO_INCREMENT,
  `OverrideType` VARCHAR(100) NULL DEFAULT NULL,
  `FromDispositionID` INT NULL DEFAULT NULL,
  `ToDispositionID` INT NULL DEFAULT NULL,
  `RequesterLogin` VARCHAR(100) NULL DEFAULT NULL,
  `ToCustomPalletID` INT NULL DEFAULT NULL,
  `ToBinName` TEXT NULL DEFAULT NULL,
  `ReasonID` INT NULL DEFAULT NULL,
  `OverrideReason` TEXT NULL DEFAULT NULL,
  `OverrideNotes` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`OverrideID`));


  CREATE TABLE `awsstg`.`disposition_override_records` (
  `RecordID` INT NOT NULL AUTO_INCREMENT,
  `OverrideID` INT NULL DEFAULT NULL,
  `AssetScanID` BIGINT NULL DEFAULT NULL,
  `SerialNumber` VARCHAR(100) NULL DEFAULT NULL,
  `MPN` VARCHAR(100) NULL DEFAULT NULL,
  `part_type` VARCHAR(100) NULL DEFAULT NULL,
  `FromDispositionID` INT NULL DEFAULT NULL,
  `FromDispositionName` VARCHAR(100) NULL DEFAULT NULL,
  `ToDispositionID` INT NULL DEFAULT NULL,
  `ToDispositionName` VARCHAR(100) NULL DEFAULT NULL,
  `FromCustomPalletID` INT NULL DEFAULT NULL,
  `ToCustomPalletID` INT NULL DEFAULT NULL,
  `ToBinName` TEXT NULL DEFAULT NULL,
  `ReasonID` INT NULL DEFAULT NULL,
  `OverrideReason` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`RecordID`));


  ALTER TABLE `awsstg`.`disposition_override_records`
ADD INDEX `fk_override_idx` (`OverrideID` ASC) VISIBLE,
ADD INDEX `asset_index` (`AssetScanID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`disposition_override_records`
ADD CONSTRAINT `fk_override`
  FOREIGN KEY (`OverrideID`)
  REFERENCES `awsstg`.`disposition_override` (`OverrideID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


  ALTER TABLE `awsstg`.`disposition_override`
ADD COLUMN `Completed` TINYINT NULL DEFAULT 0 AFTER `CreatedBy`,
ADD COLUMN `CompletedDateTime` DATETIME NULL DEFAULT NULL AFTER `Completed`,
ADD COLUMN `CompletedBy` INT NULL DEFAULT NULL AFTER `CompletedDateTime`,
ADD COLUMN `TPVRController` VARCHAR(100) NULL DEFAULT NULL AFTER `CompletedBy`;

ALTER TABLE `awsstg`.`disposition_override_records`
ADD COLUMN `Completed` TINYINT NULL DEFAULT 0 AFTER `CreatedBy`,
ADD COLUMN `TPVRController` VARCHAR(100) NULL DEFAULT NULL AFTER `Completed`,
ADD COLUMN `CompletedDate` DATETIME NULL DEFAULT NULL AFTER `TPVRController`,
ADD COLUMN `CompletedBy` INT NULL DEFAULT NULL AFTER `CompletedDate`;


CREATE TABLE `awsstg`.`material_types` (
  `MaterialTypeID` INT NOT NULL AUTO_INCREMENT,
  `MaterialType` VARCHAR(100) NULL DEFAULT NULL,
  `MaterialTypeDescription` TEXT NULL DEFAULT NULL,
  `Status` VARCHAR(10) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  `SpeedMaterialType` TINYINT NULL DEFAULT 0,
  PRIMARY KEY (`MaterialTypeID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Material Type', 'MaterialTypeList', 'MaterialTypeList', '', '1', '', '1', 'eViridis Administration', '0', '72', '0');

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Source Type', 'SourceTypeList', 'SourceTypeList', '', '1', '', '1', 'eViridis Administration', '0', '73', '0');

  CREATE TABLE `awsstg`.`aws_customers` (
  `AWSCustomerID` INT NOT NULL AUTO_INCREMENT,
  `Customer` VARCHAR(100) NULL DEFAULT NULL,
  `CustomerDescription` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`AWSCustomerID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Customer', 'AWSCustomerList', 'AWSCustomerList', '', '1', '', '1', 'eViridis Administration', '0', '74', '0');

  ALTER TABLE `awsstg`.`aws_customers` ADD COLUMN `Status` VARCHAR(10) NULL DEFAULT NULL AFTER `UpdatedBy`;

  ALTER TABLE `awsstg`.`disposition_override_assets` ADD COLUMN `ShippingContainerID` VARCHAR(100) NULL DEFAULT NULL AFTER `OverrideType`;

  ALTER TABLE `awsstg`.`users` ADD COLUMN `DispositionOverrideController` TINYINT NULL DEFAULT 0 AFTER `Program`;



ALTER TABLE `awsstg`.`disposition` ADD COLUMN `eligible_for_disposition_override` TINYINT NULL DEFAULT 0 AFTER `eligible_for_bin_consolidation`;




CREATE TABLE `awsstg`.`disposition_override_user_input_reasons` (
  `UserInputID` INT NOT NULL AUTO_INCREMENT,
  `UserInput` TEXT NULL DEFAULT NULL,
  `ActivateReasonCode` TINYINT NULL DEFAULT 0,
  `NotesRequired` TINYINT NULL DEFAULT 0,
  `Status` VARCHAR(10) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`UserInputID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'User Input Reason', 'UserInputReasonList', 'UserInputReason', '', '1', '', '1', 'Disposition Override', '0', '6', '0');


  CREATE TABLE `awsstg`.`source_type_configuration` (
  `ConfigurationID` INT NOT NULL AUTO_INCREMENT,
  `FacilityID` INT NULL DEFAULT NULL,
  `AWSCustomerID` INT NULL DEFAULT NULL,
  `MaterialTypeID` INT NULL DEFAULT NULL,
  `idCustomertype` INT NULL DEFAULT NULL,
  `Status` VARCHAR(10) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ConfigurationID`));

  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Source Type Configuration', 'SourceTypeConfigurationList', 'SourceTypeConfigurationList', '', '1', '', '1', 'eViridis Administration', '0', '75', '0');

  ALTER TABLE `awsstg`.`customer` ADD COLUMN `AWSCustomerID` INT NULL DEFAULT NULL AFTER `SourceCategory`;

  ALTER TABLE `awsstg`.`pallets` ADD COLUMN `idCustomertype` INT NULL DEFAULT NULL COMMENT 'Source Type' AFTER `WasteClassificationType`,ADD COLUMN `AWSCustomerID` INT NULL DEFAULT NULL AFTER `idCustomertype`;

  ALTER TABLE `awsstg`.`pallets` ADD COLUMN `MaterialTypeID` INT NULL DEFAULT NULL AFTER `AWSCustomerID`;

  INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('Material Type', 'Internal', 'MaterialType', 'MaterialType', 'Active');
  INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('Customer ID', 'Internal', 'Customer', 'Customer', 'Active');
  Make sure the IDs are 32 and 33


  ALTER TABLE `awsstg`.`shipping_containers` ADD COLUMN `ExpectedServersCount` INT NULL DEFAULT NULL AFTER `QuarantineToActiveAuditController`,ADD COLUMN `ScannedServersCount` INT NULL DEFAULT NULL AFTER `ExpectedServersCount`;


  New Sprint,moving Deman to FA
  ------------------------------
  INSERT INTO `awsdev`.`tabs` (`TabName`, `TabLink`, `Description`, `AccountID`, `Status`, `Dashboard`, `Order`, `TabIcon`) VALUES ('Asset Recovery', 'recovery', 'recovery', '1', '1', '0', '3', 'undo');//Make sure TabID is 166
  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('166', 'Parts Recovery', 'PartsRecoveryInfo', 'PartsRecoveryInfo', '', '1', '', '1', '0', '1', '0');
  INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('166', 'Parts Sort', 'PartsSort', 'PartsSort', '', '1', '', '1', '', '0', '2', '0');

  INSERT INTO `awsstg`.`workflow` (`workflow_id`, `workflow`, `workflow_description`, `status`) VALUES ('10', 'Parts Recovery', 'Parts Recovery', 'Active'); //Make sure workflow_id = 10

  CREATE TABLE awsstg.`Recoverytype` (
  `Recoverytypeid` int NOT NULL AUTO_INCREMENT,
  `Recoverytype` varchar(250) DEFAULT NULL,
  `Description` varchar(250) DEFAULT NULL,
  `Status` int DEFAULT NULL,
  `DateCreated` datetime DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `AccountID` int DEFAULT NULL,
  `FacilityID` int DEFAULT NULL,
  PRIMARY KEY (`Recoverytypeid`)
) ENGINE=InnoDB AUTO_INCREMENT=0

INSERT INTO `awsstg`.`Recoverytype` (`Recoverytypeid`, `Recoverytype`, `Description`, `Status`, `AccountID`, `FacilityID`) VALUES ('1', 'Container', 'Container', '1', '1', '4');

CREATE TABLE awsstg.`parttype` (
  `parttypeid` int NOT NULL AUTO_INCREMENT,
  `parttype` varchar(250) DEFAULT NULL,
  `serialized` varchar(45) DEFAULT NULL,
  `Description` varchar(250) DEFAULT NULL,
  `Status` int DEFAULT NULL,
  `DateCreated` datetime DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `AccountID` int DEFAULT NULL,
  `FacilityID` int DEFAULT NULL,
  `EligibleForExceptionRecovery` tinyint DEFAULT '0',
  PRIMARY KEY (`parttypeid`)
) ENGINE=InnoDB AUTO_INCREMENT=0;

ALTER TABLE `awsstg`.`by_products`
ADD COLUMN `RestrictForRecovery` TINYINT NULL DEFAULT 0 AFTER `WasteClassificationType`;

CREATE TABLE awsstg.`unserialized_recovery_records` (
  `UnserializedRecoveryRecordID` int NOT NULL AUTO_INCREMENT,
  `UniqueID` varchar(45) NOT NULL,
  `parttypeid` int NOT NULL,
  `EvaluationResultID` int DEFAULT '0',
  `DispositionID` int DEFAULT NULL,
  `DispositionBin` varchar(99) DEFAULT NULL,
  `ShippingContainerID` varchar(45) DEFAULT NULL,
  `ShippingContainerAddedDate` datetime DEFAULT NULL,
  `ShippingContainerAddedBy` int DEFAULT NULL,
  `DispositionAssignedDate` datetime DEFAULT NULL,
  `Quantity` int DEFAULT '0',
  `CustomPalletID` int DEFAULT NULL,
  `idPallet` varchar(45) DEFAULT NULL,
  `FacilityID` int DEFAULT NULL,
  `StatusID` int DEFAULT '0',
  `CreatedBy` int DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `ModifiedBy` int DEFAULT NULL,
  `ModifiedDate` datetime DEFAULT NULL,
  `SiteID` int DEFAULT NULL,
  `IsCommitted` tinyint DEFAULT '0',
  `CommittedDate` datetime DEFAULT NULL,
  `CommittedBy` int DEFAULT NULL,
  `COOID` int DEFAULT NULL,
  `AuditController` varchar(45) DEFAULT NULL,
  `RecoveryTypeID` int DEFAULT NULL,
  `RackID` varchar(45) DEFAULT NULL,
  `RackRecoverySerialNumber` varchar(45) DEFAULT NULL,
  `AssemblyRecoverySerialNumber` varchar(45) DEFAULT NULL,
  `recovery_type_scan_time` datetime DEFAULT NULL,
  `workstation_scan_time` datetime DEFAULT NULL,
  `origin_container_id_scan_time` datetime DEFAULT NULL,
  `part_type_scan_time` datetime DEFAULT NULL,
  `bin_scan_time` datetime DEFAULT NULL,
  `container_scan_time` datetime DEFAULT NULL,
  `originbinname` varchar(99) DEFAULT NULL,
  `originDispositionID` int DEFAULT NULL,
  `ShippedDate` date DEFAULT NULL,
  `ShippedTime` time DEFAULT NULL,
  `ShippingID` varchar(50) DEFAULT NULL,
  `ProcessEventID` bigint DEFAULT NULL,
  PRIMARY KEY (`UnserializedRecoveryRecordID`)
) ENGINE=InnoDB AUTO_INCREMENT=0;

ALTER TABLE `awsstg`.`disposition` ADD COLUMN `serialized` VARCHAR(10) NULL DEFAULT 'Yes' AFTER `eligible_for_disposition_override`;

CREATE TABLE awsstg.`Recoverytype_PartTypes` (
  `RecoverytypeParttypeid` int NOT NULL AUTO_INCREMENT,
  `Recoverytypeid` int NOT NULL,
  `RecoveryPartType` int NOT NULL,
  `EvaluationResultID` int NOT NULL,
  `DispositionID` int NOT NULL,
  `BreRequired` varchar(3) DEFAULT 'No',
  `DateCreated` date NOT NULL,
  `CreatedBy` int NOT NULL,
  `Status` tinyint DEFAULT '1',
  PRIMARY KEY (`RecoverytypeParttypeid`)
) ENGINE=InnoDB AUTO_INCREMENT=0

CREATE TABLE awsstg.`process_events` (
  `ProcessEventID` bigint NOT NULL AUTO_INCREMENT,
  `CreatedDate` datetime NOT NULL,
  `CreatedBy` int DEFAULT NULL,
  PRIMARY KEY (`ProcessEventID`)
) ENGINE=InnoDB AUTO_INCREMENT=0


ALTER TABLE `awsstg`.`asset`
ADD COLUMN `parttypeid` INT NULL DEFAULT NULL AFTER `RecentDispositionComments`,
ADD COLUMN `COOID` INT NULL DEFAULT NULL AFTER `parttypeid`;


INSERT INTO `awsstg`.`asset_status` (`StatusID`, `Status`) VALUES ('10', 'Not Committed');

ALTER TABLE `awsstg`.`asset`
ADD COLUMN `CommittedDate` DATETIME NULL DEFAULT NULL AFTER `COOID`,
ADD COLUMN `CommittedBy` INT NULL DEFAULT NULL AFTER `CommittedDate`;


CREATE TABLE `awsstg`.`parts_recovery_wip_records` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `Recoverytypeid` INT NULL DEFAULT NULL,
  `SiteID` INT NULL DEFAULT NULL,
  `idPallet` VARCHAR(100) NULL DEFAULT NULL,
  `parttypeid` INT NULL DEFAULT NULL,
  `input_id` INT NULL DEFAULT NULL,
  `COOID` INT NULL DEFAULT NULL,
  `SerialNumber` VARCHAR(100) NULL DEFAULT NULL,
  `MPN` VARCHAR(100) NULL DEFAULT NULL,
  `CustomPalletID` INT NULL DEFAULT NULL,
  `disposition_id` INT NULL DEFAULT NULL,
  `ValidSerial` TINYINT NULL DEFAULT 0,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ID`));

  ALTER TABLE `awsstg`.`speed_server_recovery`
ADD COLUMN `parttypeid` INT NULL DEFAULT NULL AFTER `MediaRecovery_AuditControllerID`,
ADD COLUMN `input_id` INT NULL DEFAULT NULL AFTER `parttypeid`,
ADD COLUMN `COOID` INT NULL DEFAULT NULL AFTER `input_id`,
ADD COLUMN `recovery_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `COOID`,
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `recovery_type_scan_time`,
ADD COLUMN `origin_container_id_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`,
ADD COLUMN `process_start_time` DATETIME NULL DEFAULT NULL AFTER `origin_container_id_scan_time`,
ADD COLUMN `process_complete_time` DATETIME NULL DEFAULT NULL AFTER `process_start_time`;


ALTER TABLE `awsstg`.`parts_recovery_wip_records`
ADD COLUMN `ServerID` INT NULL DEFAULT NULL AFTER `CreatedBy`,
ADD COLUMN `ServerSerialNumber` VARCHAR(100) NULL DEFAULT NULL AFTER `ServerID`;


  ALTER TABLE `awsstg`.`speed_media_recovery`
ADD COLUMN `parttypeid` INT NULL DEFAULT NULL AFTER `ShippedTime`,
ADD COLUMN `input_id` INT NULL DEFAULT NULL AFTER `parttypeid`,
ADD COLUMN `COOID` INT NULL DEFAULT NULL AFTER `input_id`,
ADD COLUMN `recovery_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `COOID`,
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `recovery_type_scan_time`,
ADD COLUMN `origin_container_id_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`,
ADD COLUMN `process_start_time` DATETIME NULL DEFAULT NULL AFTER `origin_container_id_scan_time`,
ADD COLUMN `process_complete_time` DATETIME NULL DEFAULT NULL AFTER `process_start_time`;


ALTER TABLE `awsstg`.`parts_recovery_wip_records`
ADD COLUMN `ServerID` INT NULL DEFAULT NULL AFTER `CreatedBy`,
ADD COLUMN `ServerSerialNumber` VARCHAR(100) NULL DEFAULT NULL AFTER `ServerID`;


  ALTER TABLE `awsstg`.`speed_media_recovery`
ADD COLUMN `parttypeid` INT NULL DEFAULT NULL AFTER `ShippedTime`,
ADD COLUMN `input_id` INT NULL DEFAULT NULL AFTER `parttypeid`,
ADD COLUMN `COOID` INT NULL DEFAULT NULL AFTER `input_id`,
ADD COLUMN `recovery_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `COOID`,
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `recovery_type_scan_time`,
ADD COLUMN `origin_container_id_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`,
ADD COLUMN `process_start_time` DATETIME NULL DEFAULT NULL AFTER `origin_container_id_scan_time`,
ADD COLUMN `process_complete_time` DATETIME NULL DEFAULT NULL AFTER `process_start_time`;

CREATE TABLE `parttype_coo` (
   `parttypecoo_id` int NOT NULL AUTO_INCREMENT,
   `parttypecoo_description` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin,
   `COOID` varchar(100) COLLATE utf8mb3_bin DEFAULT NULL,
   `parttypeid` int DEFAULT NULL,
   `created_date` datetime DEFAULT NULL,
   `created_by` int DEFAULT NULL,
   `updated_date` datetime DEFAULT NULL,
   `updated_by` int DEFAULT NULL,
   `default` tinyint DEFAULT '0',
   `status` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
   PRIMARY KEY (`parttypecoo_id`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin

CREATE TABLE `COO` (
   `COOID` int NOT NULL AUTO_INCREMENT,
   `COO` varchar(200) DEFAULT NULL,
   `Description` varchar(250) DEFAULT NULL,
   `Status` varchar(45) DEFAULT NULL,
   `DateCreated` datetime DEFAULT NULL,
   `CreatedBy` int DEFAULT NULL,
   `UpdatedDate` datetime DEFAULT NULL,
   `UpdatedBy` int DEFAULT NULL,
   PRIMARY KEY (`COOID`),
   UNIQUE KEY `COO_UNIQUE` (`COO`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

CREATE TABLE `bom_recoverytype_parttype` (
   `id_brp` int NOT NULL AUTO_INCREMENT,
   `RecoveryTypeID` int NOT NULL,
   `PartTypeID` int NOT NULL,
   `CreatedBy` int NOT NULL,
   `CreatedDate` datetime NOT NULL,
   PRIMARY KEY (`id_brp`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Part Type', 'PartTypeList', 'PartTypeList', '1', '1', 'eViridis Administration', '0', '76', '0');
INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'COO', 'COOList', 'COO', '1', '1', 'eViridis Administration', '0', '77', '0');
INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Recovery Type', 'RecoverytypeList', 'RecoverytypeList', '1', '1', 'eViridis Administration', '0', '78', '0');


CREATE TABLE awsstg.`station_container_mapping` (
  `sc_map_id` int NOT NULL AUTO_INCREMENT,
  `disposition_id` int DEFAULT NULL,
  `SiteID` int DEFAULT NULL,
  `ShippingContainerID` varchar(45) DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  PRIMARY KEY (`sc_map_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0


CREATE TABLE awsstg.`unserialized_recovery_tracking` (
  `TrackID` int NOT NULL AUTO_INCREMENT,
  `UnserializedRecoveryRecordID` int DEFAULT NULL,
  `PartType` varchar(50) DEFAULT NULL,
  `idPallet` varchar(50) DEFAULT NULL,
  `Action` text,
  `Quantity` int DEFAULT NULL,
  `ControllerLoginID` varchar(50) DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  PRIMARY KEY (`TrackID`)
) ENGINE=InnoDB AUTO_INCREMENT=0


ALTER TABLE `awsstg`.`custompallet_items`
ADD COLUMN `RecoveryTypeID` INT NULL DEFAULT NULL AFTER `MediaID`,
ADD COLUMN `UnserializedRecoveryRecordID` INT NULL DEFAULT NULL AFTER `RecoveryTypeID`,
ADD COLUMN `Quantity` INT NULL DEFAULT NULL AFTER `UnserializedRecoveryRecordID`;



ALTER TABLE `awsdev`.`shipping_container_serials`
ADD COLUMN `UnserializedRecoveryRecordID` INT NULL DEFAULT NULL AFTER `MediaSerialNumber`,
ADD COLUMN `Quantity` INT NULL DEFAULT 1 AFTER `UnserializedRecoveryRecordID`,
ADD COLUMN `FromCustomPalletID` INT NULL DEFAULT NULL AFTER `Quantity`,
ADD COLUMN `FromBinName` VARCHAR(99) NULL DEFAULT NULL AFTER `FromCustomPalletID`,
ADD COLUMN `FromDispositionID` INT NULL DEFAULT NULL AFTER `FromBinName`;


ALTER TABLE `awsstg`.`profile_type` ADD COLUMN `AssetReopenPermission` TINYINT NULL DEFAULT 0 AFTER `quarantine_user`;

ALTER TABLE `awsstg`.`pallets`
ADD COLUMN `LockedInPartsRecovery` TINYINT NULL DEFAULT 0 AFTER `MaterialTypeID`,
ADD COLUMN `LockedInPartsRecoveryBy` INT NULL DEFAULT NULL AFTER `LockedInPartsRecovery`,
ADD COLUMN `LockedInPartsRecoverySiteID` INT NULL DEFAULT NULL AFTER `LockedInPartsRecoveryBy`;


ALTER TABLE `awsdev`.`parts_recovery_wip_records` ADD COLUMN `AssetScanID` BIGINT NULL DEFAULT NULL AFTER `ServerSerialNumber`;

ALTER TABLE `awsdev`.`asset` ADD COLUMN `Recoverytypeid` INT NULL DEFAULT NULL AFTER `CommittedBy`;

ALTER TABLE `awsdev`.`asset`
ADD COLUMN `TopLevelServerID` INT NULL DEFAULT NULL AFTER `Recoverytypeid`,
ADD COLUMN `TopLevelSerial` VARCHAR(100) NULL DEFAULT NULL AFTER `TopLevelServerID`,
ADD COLUMN `TopLevelAssetScanID` BIGINT NULL DEFAULT NULL AFTER `TopLevelSerial`;

ALTER TABLE `awsdev`.`parts_recovery_wip_records`
ADD COLUMN `UpdatedDate` DATETIME NULL DEFAULT NULL AFTER `AssetScanID`,
ADD COLUMN `UpdatedBy` INT NULL DEFAULT NULL AFTER `UpdatedDate`;

ALTER TABLE `awsstg`.`asset`
ADD COLUMN `MediaRecovery_VerificationID` VARCHAR(100) NULL DEFAULT NULL AFTER `TopLevelAssetScanID`;

ALTER TABLE `awsstg`.`asset`
ADD COLUMN `MediaRecoveredTime` DATETIME NULL DEFAULT NULL AFTER `MediaRecovery_VerificationID`,
ADD COLUMN `MediaRecoveredBy` INT NULL DEFAULT NULL AFTER `MediaRecoveredTime`,
ADD COLUMN `MediaRecovery_AuditControllerID` VARCHAR(100) NULL DEFAULT NULL AFTER `MediaRecoveredBy`;

ALTER TABLE `awsstg`.`parts_recovery_wip_records`
ADD COLUMN `MediaID` INT NULL DEFAULT NULL AFTER `UpdatedBy`;

ALTER TABLE `awsstg`.`asset` ADD COLUMN `TopLevelMediaID` INT NULL DEFAULT NULL AFTER `MediaRecovery_AuditControllerID`;

INSERT INTO `awsstg`.`asset_status` (`StatusID`, `Status`) VALUES ('11', 'Closed');

ALTER TABLE `awsstg`.`asset`
ADD COLUMN `ComponentRecoveredTime` DATETIME NULL DEFAULT NULL AFTER `TopLevelMediaID`,
ADD COLUMN `ComponentRecoveredBy` VARCHAR(45) NULL DEFAULT NULL AFTER `ComponentRecoveredTime`;


ALTER TABLE `awsstg`.`speed_server_recovery`
ADD COLUMN `LockedInPartsRecovery` TINYINT NULL DEFAULT 0 AFTER `process_complete_time`,
ADD COLUMN `LockedInPartsRecoveryBy` INT NULL DEFAULT NULL AFTER `LockedInPartsRecovery`,
ADD COLUMN `LockedInPartsRecoverySiteID` INT NULL DEFAULT NULL AFTER `LockedInPartsRecoveryBy`;


ALTER TABLE `awsdev`.`asset`
ADD COLUMN `LockedInPartsRecovery` TINYINT NULL DEFAULT 0 AFTER `ComponentRecoveredBy`,
ADD COLUMN `LockedInPartsRecoveryBy` INT NULL DEFAULT NULL AFTER `LockedInPartsRecovery`,
ADD COLUMN `LockedInPartsRecoverySiteID` INT NULL DEFAULT NULL AFTER `LockedInPartsRecoveryBy`;


CREATE TABLE `rack_recovery_records` (
   `RackRecoveryRecordID` int NOT NULL AUTO_INCREMENT,
   `UniqueID` varchar(45) NOT NULL,
   `parttypeid` int NOT NULL,
   `EvaluationResultID` int DEFAULT NULL,
   `MPN` varchar(45) DEFAULT NULL,
   `DispositionID` int DEFAULT NULL,
   `DispositionBin` varchar(99) DEFAULT NULL,
   `SerialNumber` varchar(45) DEFAULT NULL,
   `CustomPalletID` int DEFAULT NULL,
   `idPallet` varchar(45) DEFAULT NULL,
   `FacilityID` int DEFAULT NULL,
   `StatusID` int DEFAULT '0',
   `CreatedBy` int DEFAULT NULL,
   `CreatedDate` datetime DEFAULT NULL,
   `ModifiedBy` int DEFAULT NULL,
   `ModifiedDate` datetime DEFAULT NULL,
   `Closed` int DEFAULT '0',
   `ClosedBy` int DEFAULT NULL,
   `ClosedDate` datetime DEFAULT NULL,
   `SiteID` int DEFAULT NULL,
   `IsCommitted` tinyint DEFAULT '0',
   `CommittedDate` datetime DEFAULT NULL,
   `CommittedBy` int DEFAULT NULL,
   `COOID` int DEFAULT NULL,
   `AuditController` varchar(45) DEFAULT NULL,
   `RecoveryTypeID` int DEFAULT NULL,
   `ShippingContainerID` varchar(50) DEFAULT NULL,
   `ShippingContainerAddedDate` datetime DEFAULT NULL,
   `ShippingContainerAddedBy` int DEFAULT NULL,
   `DispositionAssignedDate` datetime DEFAULT NULL,
   `DispositionOverrideReason` varchar(255) DEFAULT NULL,
   `idManufacturer` int DEFAULT NULL,
   `originbinname` varchar(99) DEFAULT NULL,
   `originDispositionID` int DEFAULT NULL,
   `rule_id` int DEFAULT NULL,
   `recovery_type_scan_time` datetime DEFAULT NULL,
   `workstation_scan_time` datetime DEFAULT NULL,
   `origin_container_id_scan_time` datetime DEFAULT NULL,
   `part_type_scan_time` datetime DEFAULT NULL,
   `result_scan_time` datetime DEFAULT NULL,
   `coo_scan_time` datetime DEFAULT NULL,
   `mpn_scan_time` datetime DEFAULT NULL,
   `bin_scan_time` datetime DEFAULT NULL,
   `serial_scan_time` datetime DEFAULT NULL,
   `ShippedDate` date DEFAULT NULL,
   `ShippedTime` time DEFAULT NULL,
   `ShippingID` varchar(50) DEFAULT NULL,
   `ExceptionReason` varchar(99) DEFAULT NULL,
   `idCustomer` int DEFAULT NULL,
   `MaterialType` varchar(45) DEFAULT NULL,
   `source_id_scan_time` datetime DEFAULT NULL,
   `material_type_scan_time` datetime DEFAULT NULL,
   `exception_reason_scan_time` datetime DEFAULT NULL,
   `ProcessEventID` bigint DEFAULT NULL,
   PRIMARY KEY (`RackRecoveryRecordID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

CREATE TABLE `assembly_recovery_records` (
   `AssemblyRecoveryRecordID` int NOT NULL AUTO_INCREMENT,
   `UniqueID` varchar(45) NOT NULL,
   `parttypeid` int NOT NULL,
   `EvaluationResultID` int DEFAULT NULL,
   `MPN` varchar(45) DEFAULT NULL,
   `DispositionID` int DEFAULT NULL,
   `DispositionBin` varchar(99) DEFAULT NULL,
   `SerialNumber` varchar(45) DEFAULT NULL,
   `CustomPalletID` int DEFAULT NULL,
   `idPallet` varchar(45) DEFAULT NULL,
   `FacilityID` int DEFAULT NULL,
   `StatusID` int DEFAULT '0',
   `CreatedBy` int DEFAULT NULL,
   `CreatedDate` datetime DEFAULT NULL,
   `ModifiedBy` int DEFAULT NULL,
   `ModifiedDate` datetime DEFAULT NULL,
   `Closed` tinyint DEFAULT '0',
   `ClosedBy` int DEFAULT NULL,
   `ClosedDate` datetime DEFAULT NULL,
   `SiteID` int DEFAULT NULL,
   `IsCommitted` tinyint DEFAULT '0',
   `CommittedDate` datetime DEFAULT NULL,
   `CommittedBy` int DEFAULT NULL,
   `COOID` int DEFAULT NULL,
   `AuditController` varchar(45) DEFAULT NULL,
   `RecoveryTypeID` int DEFAULT NULL,
   `ShippingContainerID` varchar(50) DEFAULT NULL,
   `ShippingContainerAddedDate` datetime DEFAULT NULL,
   `ShippingContainerAddedBy` int DEFAULT NULL,
   `DispositionAssignedDate` datetime DEFAULT NULL,
   `DispositionOverrideReason` varchar(255) DEFAULT NULL,
   `idManufacturer` int DEFAULT NULL,
   `originbinname` varchar(99) DEFAULT NULL,
   `originDispositionID` int DEFAULT NULL,
   `rule_id` int DEFAULT NULL,
   `recovery_type_scan_time` datetime DEFAULT NULL,
   `workstation_scan_time` datetime DEFAULT NULL,
   `origin_container_id_scan_time` datetime DEFAULT NULL,
   `part_type_scan_time` datetime DEFAULT NULL,
   `result_scan_time` datetime DEFAULT NULL,
   `coo_scan_time` datetime DEFAULT NULL,
   `mpn_scan_time` datetime DEFAULT NULL,
   `bin_scan_time` datetime DEFAULT NULL,
   `serial_scan_time` datetime DEFAULT NULL,
   `ShippedDate` date DEFAULT NULL,
   `ShippedTime` time DEFAULT NULL,
   `ShippingID` varchar(50) DEFAULT NULL,
   `ExceptionReason` varchar(99) DEFAULT NULL,
   `idCustomer` int DEFAULT NULL,
   `MaterialType` varchar(45) DEFAULT NULL,
   `source_id_scan_time` datetime DEFAULT NULL,
   `material_type_scan_time` datetime DEFAULT NULL,
   `exception_reason_scan_time` datetime DEFAULT NULL,
   `ProcessEventID` bigint DEFAULT NULL,
   PRIMARY KEY (`AssemblyRecoveryRecordID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci


CREATE TABLE `component_recovery_records` (
   `ComponentRecoveryRecordID` int NOT NULL AUTO_INCREMENT,
   `UniqueID` varchar(45) NOT NULL,
   `parttypeid` int NOT NULL,
   `EvaluationResultID` int DEFAULT NULL,
   `MPN` varchar(45) DEFAULT NULL,
   `DispositionID` int DEFAULT NULL,
   `DispositionBin` varchar(99) DEFAULT NULL,
   `SerialNumber` varchar(45) DEFAULT NULL,
   `CustomPalletID` int DEFAULT NULL,
   `idPallet` varchar(45) DEFAULT NULL,
   `FacilityID` int DEFAULT NULL,
   `StatusID` int DEFAULT '0',
   `CreatedBy` int DEFAULT NULL,
   `CreatedDate` datetime DEFAULT NULL,
   `ModifiedBy` int DEFAULT NULL,
   `ModifiedDate` datetime DEFAULT NULL,
   `Closed` tinyint DEFAULT '0',
   `ClosedBy` int DEFAULT NULL,
   `ClosedDate` datetime DEFAULT NULL,
   `SiteID` int DEFAULT NULL,
   `IsCommitted` tinyint DEFAULT '0',
   `CommittedDate` datetime DEFAULT NULL,
   `CommittedBy` int DEFAULT NULL,
   `COOID` int DEFAULT NULL,
   `AuditController` varchar(45) DEFAULT NULL,
   `RecoveryTypeID` int DEFAULT NULL,
   `ShippingContainerID` varchar(50) DEFAULT NULL,
   `ShippingContainerAddedDate` datetime DEFAULT NULL,
   `ShippingContainerAddedBy` int DEFAULT NULL,
   `DispositionAssignedDate` datetime DEFAULT NULL,
   `DispositionOverrideReason` varchar(255) DEFAULT NULL,
   `idManufacturer` int DEFAULT NULL,
   `originbinname` varchar(99) DEFAULT NULL,
   `originDispositionID` int DEFAULT NULL,
   `rule_id` int DEFAULT NULL,
   `recovery_type_scan_time` datetime DEFAULT NULL,
   `workstation_scan_time` datetime DEFAULT NULL,
   `origin_container_id_scan_time` datetime DEFAULT NULL,
   `part_type_scan_time` datetime DEFAULT NULL,
   `result_scan_time` datetime DEFAULT NULL,
   `coo_scan_time` datetime DEFAULT NULL,
   `mpn_scan_time` datetime DEFAULT NULL,
   `bin_scan_time` datetime DEFAULT NULL,
   `serial_scan_time` datetime DEFAULT NULL,
   `ShippedDate` date DEFAULT NULL,
   `ShippedTime` time DEFAULT NULL,
   `ShippingID` varchar(50) DEFAULT NULL,
   `ExceptionReason` varchar(99) DEFAULT NULL,
   `idCustomer` int DEFAULT NULL,
   `MaterialType` varchar(45) DEFAULT NULL,
   `source_id_scan_time` datetime DEFAULT NULL,
   `material_type_scan_time` datetime DEFAULT NULL,
   `exception_reason_scan_time` datetime DEFAULT NULL,
   `ProcessEventID` bigint DEFAULT NULL,
   PRIMARY KEY (`ComponentRecoveryRecordID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci



INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `Order`) VALUES ('166', 'Pending Save', 'RecoveryPendingSave', 'RecoveryPendingSave', '1', '1', '5');
INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `Order`) VALUES ('166', 'Pending Close', 'RecoveryPendingClose', 'RecoveryPendingClose', '1', '1', '6');

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Change Location Group', 'ChangeLocationGroup', 'group change', '1', '1', 'Warehouse Management', '0', '55', '0');
INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Bin/Container Move', 'ChangeLocation', 'move bin/container', '1', '1', 'Warehouse Management', '0', '56', '0');


CREATE TABLE `awsstg`.`recover_configuration` (
  `ConfigurationID` INT NOT NULL AUTO_INCREMENT,
  `FromDispositionID` INT NULL DEFAULT NULL,
  `Recoverytypeid` INT NULL DEFAULT NULL,
  `parttypeid` INT NULL DEFAULT NULL,
  `input_id` INT NULL DEFAULT NULL,
  `ToDispositionID` INT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  `UpdatedDate` DATETIME NULL DEFAULT NULL,
  `UpdatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ConfigurationID`));

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('166', 'Recover Configuration', 'RecoverConfigurationList', 'RecoverConfigurationList', '', '1', '', '1', '', '0', '7', '0');

ALTER TABLE `awsstg`.`recover_configuration` ADD COLUMN `FacilityID` INT NULL DEFAULT NULL AFTER `UpdatedBy`;

ALTER TABLE `awsstg`.`asset`
ADD COLUMN `TopLevelUniqueIdentifier` VARCHAR(100) NULL DEFAULT NULL AFTER `LockedInPartsRecoverySiteID`;

ALTER TABLE `awsstg`.`parts_recovery_wip_records` ADD COLUMN `TopLevelUniqueIdentifier` VARCHAR(100) NULL DEFAULT NULL AFTER `MediaID`;


ALTER TABLE `awsstg`.`recover_configuration` ADD COLUMN `DefaultValue` TINYINT NULL DEFAULT 0 AFTER `FacilityID`;

ALTER TABLE `awsstg`.`recover_configuration` ADD COLUMN `Status` VARCHAR(10) NULL DEFAULT NULL AFTER `DefaultValue`;

ALTER TABLE `awsstg`.`asset`
ADD COLUMN `PendingSaveDate` DATETIME NULL DEFAULT NULL AFTER `TopLevelUniqueIdentifier`,
ADD COLUMN `PendingSaveBy` INT NULL DEFAULT NULL AFTER `PendingSaveDate`;

ALTER TABLE `awsstg`.`speed_server_recovery`
ADD COLUMN `PendingSaveDate` DATETIME NULL DEFAULT NULL AFTER `LockedInPartsRecoverySiteID`,
ADD COLUMN `PendingSaveBy` INT NULL DEFAULT NULL AFTER `PendingSaveDate`;

ALTER TABLE `awsstg`.`speed_media_recovery`
ADD COLUMN `PendingSaveDate` DATETIME NULL DEFAULT NULL AFTER `process_complete_time`,
ADD COLUMN `PendingSaveBy` INT NULL DEFAULT NULL AFTER `PendingSaveDate`;

ALTER TABLE `awsstg`.`asset`
ADD COLUMN `part_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `PendingSaveBy`,
ADD COLUMN `mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `part_type_scan_time`,
ADD COLUMN `result_scan_time` DATETIME NULL DEFAULT NULL AFTER `mpn_scan_time`,
ADD COLUMN `coo_scan_time` DATETIME NULL DEFAULT NULL AFTER `result_scan_time`,
ADD COLUMN `bin_scan_time` DATETIME NULL DEFAULT NULL AFTER `coo_scan_time`,
ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `bin_scan_time`,
ADD COLUMN `recovery_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `recovery_type_scan_time`,
ADD COLUMN `origin_container_id_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`;

ALTER TABLE `awsstg`.`asset` ADD COLUMN `ProcessEventID` INT NULL DEFAULT NULL AFTER `origin_container_id_scan_time`;

ALTER TABLE `awsstg`.`asset`
ADD COLUMN `process_start_time` DATETIME NULL DEFAULT NULL AFTER `ProcessEventID`,
ADD COLUMN `process_complete_time` DATETIME NULL DEFAULT NULL AFTER `process_start_time`;


ALTER TABLE `awsstg`.`parts_recovery_wip_records`
ADD COLUMN `part_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `TopLevelUniqueIdentifier`,
ADD COLUMN `mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `part_type_scan_time`,
ADD COLUMN `result_scan_time` DATETIME NULL DEFAULT NULL AFTER `mpn_scan_time`,
ADD COLUMN `coo_scan_time` DATETIME NULL DEFAULT NULL AFTER `result_scan_time`,
ADD COLUMN `bin_scan_time` DATETIME NULL DEFAULT NULL AFTER `coo_scan_time`,
ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `bin_scan_time`,
ADD COLUMN `recovery_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `recovery_type_scan_time`,
ADD COLUMN `origin_container_id_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`;

ALTER TABLE `awsstg`.`speed_server_recovery`
DROP COLUMN `origin_container_id_scan_time`,
DROP COLUMN `workstation_scan_time`,
DROP COLUMN `recovery_type_scan_time`;

ALTER TABLE `awsstg`.`speed_server_recovery`
ADD COLUMN `part_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `PendingSaveBy`,
ADD COLUMN `mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `part_type_scan_time`,
ADD COLUMN `result_scan_time` DATETIME NULL DEFAULT NULL AFTER `mpn_scan_time`,
ADD COLUMN `coo_scan_time` DATETIME NULL DEFAULT NULL AFTER `result_scan_time`,
ADD COLUMN `bin_scan_time` DATETIME NULL DEFAULT NULL AFTER `coo_scan_time`,
ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `bin_scan_time`,
ADD COLUMN `recovery_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `recovery_type_scan_time`,
ADD COLUMN `origin_container_id_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`;


ALTER TABLE `awsstg`.`speed_media_recovery`
DROP COLUMN `origin_container_id_scan_time`,
DROP COLUMN `workstation_scan_time`,
DROP COLUMN `recovery_type_scan_time`;


ALTER TABLE `awsstg`.`speed_media_recovery`
ADD COLUMN `part_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `PendingSaveBy`,
ADD COLUMN `mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `part_type_scan_time`,
ADD COLUMN `result_scan_time` DATETIME NULL DEFAULT NULL AFTER `mpn_scan_time`,
ADD COLUMN `coo_scan_time` DATETIME NULL DEFAULT NULL AFTER `result_scan_time`,
ADD COLUMN `bin_scan_time` DATETIME NULL DEFAULT NULL AFTER `coo_scan_time`,
ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `bin_scan_time`,
ADD COLUMN `recovery_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `recovery_type_scan_time`,
ADD COLUMN `origin_container_id_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`;


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Outbound Container Consolidation', 'OutboundContainerConsolidation', '', '', '1', '', '1', 'eViridis Administration', '0', '60', '0');

By default all existing disposition in the prod should be Serialized Yes

ALTER TABLE `awsdev`.`asset` ADD COLUMN `WasteCode` VARCHAR(100) NULL DEFAULT NULL AFTER `process_complete_time`;

ALTER TABLE awsdev.speed_media_recovery  ADD COLUMN `WasteCode` VARCHAR(100) NULL DEFAULT NULL AFTER `origin_container_id_scan_time`;

ALTER TABLE awsdev.speed_server_recovery  ADD COLUMN `WasteCode` VARCHAR(100) NULL DEFAULT NULL AFTER `origin_container_id_scan_time`;

ALTER TABLE `awsdev`.`speed_media_recovery`
ADD COLUMN `TPVRReason` TEXT NULL DEFAULT NULL AFTER `origin_container_id_scan_time`,
ADD COLUMN `TPVRControllerScanTime` DATETIME NULL DEFAULT NULL AFTER `TPVRReason`;

ALTER TABLE `awsdev`.`speed_server_recovery`
ADD COLUMN `TPVRReason` TEXT NULL DEFAULT NULL AFTER `WasteCode`,
ADD COLUMN `TPVRControllerScanTime` DATETIME NULL DEFAULT NULL AFTER `TPVRReason`;

ALTER TABLE `awsdev`.`site` ADD COLUMN `Recoverytypeid` INT NULL DEFAULT NULL AFTER `GroupID`;

ALTER TABLE `awsdev`.`parts_recovery_wip_records` ADD COLUMN `WasteCode` VARCHAR(100) NULL DEFAULT NULL AFTER `origin_container_id_scan_time`;

ALTER TABLE `awsdev`.`unserialized_recovery_records` ADD COLUMN `WasteCode` VARCHAR(100) NULL DEFAULT NULL AFTER `ProcessEventID`;

ALTER TABLE `awsstg`.`parts_recovery_wip_records` ADD COLUMN `idManufacturer` INT NULL DEFAULT NULL AFTER `WasteCode`;

ALTER TABLE `awsstg`.`pallets` ADD COLUMN `WasteCode` VARCHAR(100) NULL DEFAULT NULL AFTER `LockedInPartsRecoverySiteID`;


ALTER TABLE `awsdev`.`disposition_override` ADD COLUMN `notes_scan_time` DATETIME NULL DEFAULT NULL AFTER `TPVRController`;

ALTER TABLE `awsdev`.`disposition_override_assets` ADD COLUMN `notes_scan_time` DATETIME NULL DEFAULT NULL AFTER `ShippingContainerID`;

ALTER TABLE `awsstg`.`shipping_containers` ADD COLUMN `container_added_to_shipment_time` DATETIME NULL DEFAULT NULL AFTER `ScannedServersCount`;

ALTER TABLE `awsstg`.`shipping_container_serials`
ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `FromDispositionID`,
ADD COLUMN `mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `part_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `mpn_scan_time`,
ADD COLUMN `coo_scan_time` DATETIME NULL DEFAULT NULL AFTER `part_type_scan_time`,
ADD COLUMN `tpvr_request_scan_time` DATETIME NULL DEFAULT NULL AFTER `coo_scan_time`,
ADD COLUMN `controller_scan_time` DATETIME NULL DEFAULT NULL AFTER `tpvr_request_scan_time`;


ALTER TABLE `awsstg`.`asset_failure_analysis`
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `ActualSerialNumber`,
ADD COLUMN `rig_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`,
ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `rig_scan_time`,
ADD COLUMN `mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `part_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `mpn_scan_time`,
ADD COLUMN `result_scan_time` DATETIME NULL DEFAULT NULL AFTER `part_type_scan_time`,
ADD COLUMN `bin_scan_time` DATETIME NULL DEFAULT NULL AFTER `result_scan_time`,
ADD COLUMN `coo_scan_time` DATETIME NULL DEFAULT NULL AFTER `bin_scan_time`;


ALTER TABLE `awsstg`.`asset_repair`
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `ActualSerialNumber`,
ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`,
ADD COLUMN `mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `part_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `mpn_scan_time`,
ADD COLUMN `result_scan_time` DATETIME NULL DEFAULT NULL AFTER `part_type_scan_time`,
ADD COLUMN `bin_scan_time` DATETIME NULL DEFAULT NULL AFTER `result_scan_time`,
ADD COLUMN `coo_scan_time` DATETIME NULL DEFAULT NULL AFTER `bin_scan_time`;

ALTER TABLE `awsstg`.`inventory`
ADD COLUMN `recovered_serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `ShippingContainerAddedBy`,
ADD COLUMN `recovered_mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `recovered_serial_scan_time`;


ALTER TABLE `awsstg`.`asset_repair_media_details`
ADD COLUMN `ingested_serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `InventoryID`,
ADD COLUMN `ingested_mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `ingested_serial_scan_time`;


ALTER TABLE `awsstg`.`asset_sanitization`
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `ActualSerialNumber`,
ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`,
ADD COLUMN `mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `part_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `mpn_scan_time`,
ADD COLUMN `result_scan_time` DATETIME NULL DEFAULT NULL AFTER `part_type_scan_time`,
ADD COLUMN `seal_scan_time` DATETIME NULL DEFAULT NULL AFTER `result_scan_time`,
ADD COLUMN `bin_scan_time` DATETIME NULL DEFAULT NULL AFTER `seal_scan_time`,
ADD COLUMN `coo_scan_time` DATETIME NULL DEFAULT NULL AFTER `bin_scan_time`,
ADD COLUMN `controller_scan_time` DATETIME NULL DEFAULT NULL AFTER `coo_scan_time`;

ALTER TABLE `awsstg`.`asset_sanitization_media_details`
ADD COLUMN `ingested_serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `InventoryID`,
ADD COLUMN `ingested_mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `ingested_serial_scan_time`;

ALTER TABLE `awsstg`.`asset_rma_investigation`
ADD COLUMN `workstation_scan_time` DATETIME NULL DEFAULT NULL AFTER `ActualSerialNumber`,
ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `workstation_scan_time`,
ADD COLUMN `mpn_scan_time` DATETIME NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `part_type_scan_time` DATETIME NULL DEFAULT NULL AFTER `mpn_scan_time`,
ADD COLUMN `result_scan_time` DATETIME NULL DEFAULT NULL AFTER `part_type_scan_time`,
ADD COLUMN `bin_scan_time` DATETIME NULL DEFAULT NULL AFTER `result_scan_time`,
ADD COLUMN `coo_scan_time` DATETIME NULL DEFAULT NULL AFTER `bin_scan_time`;


ALTER TABLE `awsstg`.`disposition_override_records` ADD COLUMN `notes_scan_time` DATETIME NULL DEFAULT NULL AFTER `CompletedBy`;

ALTER TABLE `awsdev`.`asset`
ADD COLUMN `event_id` VARCHAR(15) NULL DEFAULT NULL AFTER `WasteCode`,
ADD COLUMN `batch_event_flag` VARCHAR(2) NULL DEFAULT NULL AFTER `event_id`;


ALTER TABLE `awsstg`.`speed_server_recovery`
ADD COLUMN `event_id` VARCHAR(15) NULL DEFAULT NULL AFTER `TPVRControllerScanTime`,
ADD COLUMN `batch_event_flag` VARCHAR(2) NULL DEFAULT NULL AFTER `event_id`;


ALTER TABLE `awsdev`.`speed_media_recovery`
ADD COLUMN `event_id` VARCHAR(15) NULL DEFAULT NULL AFTER `WasteCode`,
ADD COLUMN `batch_event_flag` VARCHAR(2) NULL DEFAULT NULL AFTER `event_id`;


ALTER TABLE `awsstg`.`unserialized_recovery_records`
ADD COLUMN `event_id` VARCHAR(15) NULL DEFAULT NULL AFTER `WasteCode`,
ADD COLUMN `batch_event_flag` VARCHAR(2) NULL DEFAULT NULL AFTER `event_id`;


ALTER TABLE `awsdev`.`labor_tracking`
ADD COLUMN `event_id` VARCHAR(45) NULL DEFAULT NULL AFTER `override_reason`,
ADD COLUMN `event_record_time` DATETIME NULL DEFAULT NULL AFTER `event_id`,
ADD COLUMN `event_record_login_id` INT NULL DEFAULT NULL AFTER `event_record_time`;


ALTER TABLE awsstg.disposition_override_assets
ADD COLUMN event_id VARCHAR(45) NULL DEFAULT NULL AFTER notes_scan_time,
ADD COLUMN batch_event_flag VARCHAR(45) NULL DEFAULT NULL AFTER event_id;

ALTER TABLE awsstg.asset_failure_analysis
ADD COLUMN event_id VARCHAR(45) NULL DEFAULT NULL AFTER coo_scan_time,
ADD COLUMN batch_event_flag VARCHAR(45) NULL DEFAULT NULL AFTER event_id;

ALTER TABLE awsstg.bin_consolidation_asset_movement
ADD COLUMN event_id VARCHAR(45) NULL DEFAULT NULL AFTER InventoryID,
ADD COLUMN batch_event_flag VARCHAR(45) NULL DEFAULT NULL AFTER event_id;

ALTER TABLE awsstg.asset_repair
ADD COLUMN event_id VARCHAR(45) NULL DEFAULT NULL AFTER coo_scan_time,
ADD COLUMN batch_event_flag VARCHAR(45) NULL DEFAULT NULL AFTER event_id;

ALTER TABLE awsstg.asset_rma_investigation
ADD COLUMN event_id VARCHAR(45) NULL DEFAULT NULL AFTER coo_scan_time,
ADD COLUMN batch_event_flag VARCHAR(45) NULL DEFAULT NULL AFTER event_id;

ALTER TABLE awsstg.asset_sanitization
ADD COLUMN event_id VARCHAR(45) NULL DEFAULT NULL AFTER controller_scan_time,
ADD COLUMN batch_event_flag VARCHAR(45) NULL DEFAULT NULL AFTER event_id;

ALTER TABLE `awsstg`.`pallets`
ADD COLUMN `Reopened` TINYINT NULL DEFAULT 0 AFTER `WasteCode`,
ADD COLUMN `ReopenedDateTime` DATETIME NULL DEFAULT NULL AFTER `Reopened`,
ADD COLUMN `ReopenedBy` INT NULL DEFAULT NULL AFTER `ReopenedDateTime`;


INSERT INTO `awsdev`.`tabs` (`TabName`, `TabLink`, `Description`, `AccountID`, `Status`, `Dashboard`, `Order`, `TabIcon`) VALUES ('Destruction', 'destruction', 'destruction', '1', '1', '0', '18', 'undo');

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('167', 'Pending Destruction', 'PendingDestruction', 'PendingDestruction', '1', '1', '0', '1', '0');
INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('167', 'Bin Destruction', 'BinDestruction', 'BinDestruction', '1', '1', '0', '2', '0');
INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('167', 'Bulk Serial Destruction', 'BulkSerialDestruction', 'BulkSerialDestruction', '1', '1', '0', '3', '0');
INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('167', 'Destruction Configuration', 'DestructionConfigurationList', 'DestructionConfigurationList', '1', '1', '0', '4', '0');

CREATE TABLE `Destruction_Configuration` (
   `Destruction_Configuration_ID` int NOT NULL AUTO_INCREMENT,
   `FacilityID` int DEFAULT NULL,
   `parttypeid` int DEFAULT NULL,
   `disposition_id` int DEFAULT NULL,
   `RigID` int DEFAULT NULL,
   `Post_Destruction_Serialized` varchar(45) DEFAULT NULL,
   `byproduct_id` int DEFAULT NULL,
   `Post_Destruction_Disposition_id` int DEFAULT NULL,
   `Status` varchar(45) DEFAULT NULL,
   `DateCreated` datetime DEFAULT NULL,
   `DateUpdated` datetime DEFAULT NULL,
   `CreatedBy` int DEFAULT NULL,
   `UpdatedBy` int DEFAULT NULL,
   PRIMARY KEY (`Destruction_Configuration_ID`),
   UNIQUE KEY `Destruction_Configuration_ID_UNIQUE` (`Destruction_Configuration_ID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

ALTER TABLE `awsdev`.`disposition`
ADD COLUMN `destruction_disposition` VARCHAR(10) NULL AFTER `serialized`;

CREATE TABLE `destruction_history` (
   `DestructionHistoryID` int NOT NULL AUTO_INCREMENT,
   `RackRecoveryRecordID` int DEFAULT NULL,
   `AssemblyRecoveryRecordID` int DEFAULT NULL,
   `ComponentRecoveryRecordID` int DEFAULT NULL,
   `UnserializedRecoveryRecordID` int DEFAULT NULL,
   `FromCustomPalletID` int DEFAULT NULL,
   `ToCustomPalletID` int DEFAULT NULL,
   `FromBinName` varchar(99) DEFAULT NULL,
   `ToBinName` varchar(99) DEFAULT NULL,
   `ToShippingContainerID` varchar(99) DEFAULT NULL,
   `FromDispositionID` int DEFAULT NULL,
   `ToDispositionID` int DEFAULT NULL,
   `CreatedDate` datetime DEFAULT NULL,
   `CreatedBy` int DEFAULT NULL,
   `SiteID` int DEFAULT NULL,
   `RigID` int DEFAULT NULL,
   `AuditController` varchar(99) DEFAULT NULL,
   `bulk_transaction_flag` tinyint DEFAULT '0',
   `bulk_transaction_id` int DEFAULT NULL,
   `FacilityID` int DEFAULT NULL,
   `origin_bin_scan_time` datetime DEFAULT NULL,
   `controller_scan_time` datetime DEFAULT NULL,
   `container_scan_time` datetime DEFAULT NULL,
   `destruction_rig_scan_time` datetime DEFAULT NULL,
   `workstation_scan_time` datetime DEFAULT NULL,
   `serial_scan_time` datetime DEFAULT NULL,
   PRIMARY KEY (`DestructionHistoryID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci


INSERT INTO `awsdev`.`workflow` (`workflow_id`, `workflow`, `workflow_description`, `status`) VALUES ('20', 'Parts Sort', 'Parts Sort', 'Active'); (Make sure 20 is the workflow_id)

CREATE TABLE awsstg.`sort_criteria` (
  `SortCriteriaID` int NOT NULL AUTO_INCREMENT,
  `CriteriaName` varchar(99) NOT NULL,
  `Status` tinyint NOT NULL DEFAULT '1',
  `Priority` tinyint NOT NULL,
  PRIMARY KEY (`SortCriteriaID`)
) ENGINE=InnoDB AUTO_INCREMENT=0

CREATE TABLE awsstg.`sortconfiguration` (
  `sortconfigurationid` int NOT NULL AUTO_INCREMENT,
  `FacilityID` int DEFAULT NULL,
  `GroupID` int DEFAULT NULL,
  `disposition_id` int DEFAULT NULL,
  `MPNRequired` varchar(45) DEFAULT NULL,
  `mpn_id` varchar(45) DEFAULT 'All',
  `CustomPalletID` varchar(100) DEFAULT NULL,
  `LocationID` varchar(45) DEFAULT NULL,
  `MaximumAssets` int DEFAULT NULL,
  `AccountID` int DEFAULT NULL,
  `DateCreated` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `BinName` varchar(250) DEFAULT NULL,
  `part_spec_id` varchar(99) DEFAULT 'All',
  `COO` varchar(45) DEFAULT 'All',
  `SortCriteriaID` int DEFAULT NULL,
  `Status` tinyint NOT NULL DEFAULT '1',
  `parttypeid` int DEFAULT NULL,
  PRIMARY KEY (`sortconfigurationid`),
  UNIQUE KEY `sortconfigurationid_UNIQUE` (`sortconfigurationid`)
) ENGINE=InnoDB AUTO_INCREMENT=0


CREATE TABLE awsstg.`EvaluationResult` (
  `EvaluationResultID` int NOT NULL AUTO_INCREMENT,
  `EvaluationResult` varchar(200) DEFAULT NULL,
  `FacilityID` int DEFAULT NULL,
  `Description` varchar(250) DEFAULT NULL,
  `Status` varchar(45) DEFAULT NULL,
  `DateCreated` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `AccountID` int DEFAULT NULL,
  PRIMARY KEY (`EvaluationResultID`),
  UNIQUE KEY `EvaluationResultID_UNIQUE` (`EvaluationResultID`)
) ENGINE=InnoDB AUTO_INCREMENT=0


CREATE TABLE awsdev.`parts_sort_scan_time` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `SiteID` int DEFAULT NULL,
  `CustomPalletID` int DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=0


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Sort Configuration', 'SortConfiguration', 'SortConfiguration', '', '1', '', '1', 'eViridis Administration', '0', '79', '0');

CREATE TABLE awsstg.`WorkstationConfiguration` (
  `GroupID` int NOT NULL AUTO_INCREMENT,
  `GroupName` varchar(100) DEFAULT NULL,
  `Status` int DEFAULT NULL,
  `AccountID` int DEFAULT NULL,
  `DateCreated` datetime DEFAULT NULL,
  `DateUpdated` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `FacilityID` int DEFAULT NULL,
  PRIMARY KEY (`GroupID`),
  UNIQUE KEY `GroupID_UNIQUE` (`GroupID`)
) ENGINE=InnoDB AUTO_INCREMENT=0

ALTER TABLE `awsstg`.`catlog_creation` ADD COLUMN `part_spec_id` VARCHAR(99) NULL DEFAULT NULL AFTER `UploadID`;


CREATE TABLE awsstg.`WorkstationConfiguration_mapping` (
  `GroupMappingID` int NOT NULL AUTO_INCREMENT,
  `GroupID` int DEFAULT NULL,
  `SiteID` int DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  PRIMARY KEY (`GroupMappingID`)
) ENGINE=InnoDB AUTO_INCREMENT=0

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Work Station Configuration', 'WorkstationConfiguration', 'work station management', '1', '1', 'Warehouse Management', '0', '57', '0');




CREATE TABLE awsstg.`parts_sort_history` (
  `SortID` int NOT NULL AUTO_INCREMENT,
  `PartsSortScanTimeID` int DEFAULT NULL,
  `SerialNumber` varchar(100) DEFAULT NULL,
  `AssetScanID` bigint DEFAULT NULL,
  `SiteID` int DEFAULT NULL,
  `input_id` int DEFAULT NULL,
  `MPN` varchar(100) DEFAULT NULL,
  `idPallet` varchar(100) DEFAULT NULL,
  `FromCustomPalletID` int DEFAULT NULL,
  `ToCustomPalletID` int DEFAULT NULL,
  `FromDisposition` int DEFAULT NULL,
  `ToDisposition` int DEFAULT NULL,
  `rule_id` int DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `COOID` int DEFAULT NULL,
  `serial_scan_time` datetime DEFAULT NULL,
  `mpn_scan_time` datetime DEFAULT NULL,
  `origin_bin_scan_time` datetime DEFAULT NULL,
  `evaluation_result_scan_time` datetime DEFAULT NULL,
  `destination_bin_scan_time` datetime DEFAULT NULL,
  `workstation_scan_time` datetime DEFAULT NULL,
  `workstation_group_id` int DEFAULT NULL,
  `LocationName` varchar(200) DEFAULT NULL,
  `ComponentRecoveryRecordID` int DEFAULT NULL,
  PRIMARY KEY (`SortID`)
) ENGINE=InnoDB AUTO_INCREMENT=0

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('166', 'Bin Sort', 'BinSort', 'BinSort', '', '1', '', '1', '', '0', '3', '0');

ALTER TABLE `awsstg`.`asset` ADD COLUMN `DestructionControllerID` VARCHAR(100) NULL DEFAULT NULL AFTER `batch_event_flag`;


ALTER TABLE `awsstg`.`destruction_history`
ADD COLUMN `AssetScanID` BIGINT NULL DEFAULT NULL AFTER `serial_scan_time`,
ADD COLUMN `ServerID` INT NULL DEFAULT NULL AFTER `AssetScanID`,
ADD COLUMN `MediaID` INT NULL DEFAULT NULL AFTER `ServerID`;


ALTER TABLE `awsstg`.`speed_server_recovery` ADD COLUMN `DestructionControllerID` VARCHAR(100) NULL DEFAULT NULL AFTER `batch_event_flag`;

ALTER TABLE `awsstg`.`speed_media_recovery` ADD COLUMN `DestructionControllerID` VARCHAR(100) NULL DEFAULT NULL AFTER `batch_event_flag`;


CREATE TABLE awsstg.`bin_distruction_process` (
  `ProcessID` int NOT NULL AUTO_INCREMENT,
  `SiteID` int DEFAULT NULL,
  `RigID` int DEFAULT NULL,
  `SourceCustomPalletID` int DEFAULT NULL,
  `SourceBinName` text,
  `SourceBinDisposition_id` int DEFAULT NULL,
  `AuditController` varchar(100) DEFAULT NULL,
  `DestinationCustomPalletID` int DEFAULT NULL,
  `DestinationBinName` text,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `ShippingContainerID` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`ProcessID`)
) ENGINE=InnoDB AUTO_INCREMENT=0


CREATE TABLE awsstg.`ExceptionReason` (
  `ExceptionReasonID` int NOT NULL AUTO_INCREMENT,
  `ExceptionReason` varchar(200) DEFAULT NULL,
  `Status` varchar(45) DEFAULT NULL,
  `DateCreated` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `Default` tinyint DEFAULT '0',
  PRIMARY KEY (`ExceptionReasonID`)
) ENGINE=InnoDB AUTO_INCREMENT=0

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('166', 'Unknown Recovery', 'ExceptionPartsRecovery', 'ExceptionPartsRecovery', '', '1', '', '1', '', '0', '3', '0');


ALTER TABLE `awsstg`.`asset`
ADD COLUMN `CustomerID` INT NULL DEFAULT NULL AFTER `DestructionControllerID`,
ADD COLUMN `MaterialType` VARCHAR(100) NULL DEFAULT NULL AFTER `CustomerID`,
ADD COLUMN `UnknownRecovery` TINYINT NULL DEFAULT 0 AFTER `MaterialType`;

INSERT INTO `awsstg`.`asset_status` (`StatusID`, `Status`) VALUES ('12', 'Shreded');

ALTER TABLE `awsstg`.`bin_distruction_process` ADD COLUMN `DestinationBinDisposition_id` INT NULL DEFAULT NULL AFTER `ShippingContainerID`;

ALTER TABLE `awsstg`.`users` ADD COLUMN `DestructionController` TINYINT NULL DEFAULT 0 AFTER `DispositionOverrideController`;

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Exception Reason', 'ExceptionReasonList', 'Exception Reason', '1', '1', 'eViridis Administration', '1', '82', '0');

ALTER TABLE `awsstg`.`speed_bulk_media_process_details` ADD COLUMN `AssetScanID` BIGINT NULL DEFAULT NULL AFTER `CreatedBy`;

<<<<<<< HEAD
ALTER TABLE `awsstg`.`speed_bulk_media_process_details` ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `AssetScanID`;

ALTER TABLE `awsstg`.`speed_bulk_media_process_details` ADD COLUMN `ServerID` INT NULL DEFAULT NULL AFTER `serial_scan_time`;
=======
CREATE TABLE `awsdev`.`LaborTrackerReasonCode` (
  `LaborTrackerReasonCodeID` INT NOT NULL AUTO_INCREMENT,
  `LaborTrackerReasonCode` VARCHAR(99) NULL,
  `Description` VARCHAR(250) NULL,
  `Status` INT NULL,
  PRIMARY KEY (`LaborTrackerReasonCodeID`),
  UNIQUE INDEX `LaborTrackerReasonCodeID_UNIQUE` (`LaborTrackerReasonCodeID` ASC) VISIBLE);

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Labor Tracker Override Reason Code', 'LaborTrackerReasonCodeList', 'LaborTrackerReasonCodeList', '1', '1', 'eViridis Administration', '1', '83', '0');

ALTER TABLE `awsdev`.`users`
ADD COLUMN `Shift` VARCHAR(99) NULL DEFAULT NULL AFTER `DestructionController`,
ADD COLUMN `AccountType` VARCHAR(45) NULL DEFAULT NULL AFTER `Shift`,
ADD COLUMN `Manager` VARCHAR(99) NULL DEFAULT NULL AFTER `AccountType`;

ALTER TABLE `awsstg`.`speed_bulk_media_process_details` ADD COLUMN `serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `AssetScanID`;
>>>>>>> d314a1c3f499d542469a1c38a9034fbf382e1000


Create New RecoveryType with exact match "Unknown" for all facilites in Recoverytype table

ALTER TABLE `awsdev`.`users`
ADD COLUMN `OtherManager` VARCHAR(99) NULL DEFAULT NULL AFTER `Manager`;

INSERT INTO `awsstg`.`tabs` (`TabName`, `TabLink`, `Description`, `AccountID`, `Status`, `Dashboard`, `Order`, `TabIcon`) VALUES ('Allocation', 'Allocation', 'Allocation', '1', '1', '0', '19', 'assignment_turned_in');


CREATE TABLE `awsstg`.`pending_save` (
  `PendingSaveID` INT NOT NULL AUTO_INCREMENT,
  `Recoverytypeid` INT NULL DEFAULT NULL,
  `Recoverytype` VARCHAR(100) NULL DEFAULT NULL,
  `SiteID` INT NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  `UniqueIdentifier` VARCHAR(100) NULL DEFAULT NULL,
  `IdentifierProcessedUser` INT NULL DEFAULT NULL,
  `IdentifierProcessedTime` DATETIME NULL DEFAULT NULL,
  `IdentifierClosedUser` INT NULL DEFAULT NULL,
  `IdentifierClosedBy` INT NULL DEFAULT NULL,
  `IdentifierTable` VARCHAR(100) NULL DEFAULT NULL,
  PRIMARY KEY (`PendingSaveID`));

  ALTER TABLE `awsstg`.`pending_save` CHANGE COLUMN `IdentifierClosedBy` `IdentifierClosedTime` DATETIME NULL DEFAULT NULL ;

  CREATE TABLE `awsdev`.`Program` (
  `ProgramID` INT NOT NULL AUTO_INCREMENT,
  `ProgramName` VARCHAR(99) NULL,
  `Description` VARCHAR(250) NULL,
  `Status` INT NULL,
  PRIMARY KEY (`ProgramID`),
  UNIQUE INDEX `ProgramID_UNIQUE` (`ProgramID` ASC) VISIBLE);

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Program Configuration', 'ProgramConfigurationList', 'ProgramConfigurationList', '1', '1', 'eViridis Administration', '0', '85', '0');

ALTER TABLE `awsdev`.`users`
CHANGE COLUMN `Shift` `Shift` VARCHAR(99) NULL DEFAULT 'n/a' ;

ALTER TABLE `awsstg`.`catlog_creation`
ADD COLUMN `cluster_eligibility` VARCHAR(100) NULL DEFAULT NULL AFTER `part_spec_id`;


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Labour Tracking Report', 'LabourTrackingReport', 'LabourTrackingReport', '1', '1', 'eViridis Administration', '0', '86', '0');

ALTER TABLE `awsstg`.`labor_tracking`
ADD COLUMN `event_dateto` DATE NULL DEFAULT NULL AFTER `event_date`;

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Re-Assign Serial', 'ReAssign', '1', '1', 'eViridis Administration', '0', '87', '0');

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Re-Disposition Serial', 'ReDisposition', '1', '1', 'eViridis Administration', '0', '88', '0');

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`) VALUES ('153', 'Re-Assign Container', 'ContainerAttribute', 'Re-Assign Container', '1', '1', 'eViridis Administration', '0', '89');


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('156', 'Track Integration', 'TrackIntegrations', 'TrackIntegration', ' ', '1', ' ', '1', '0', '11', '0');

asn_assets table add index for idPallet

speed_sns_messages add index for idPallet,MediaSerialNumber,ServerSerialNumber


CREATE TABLE awsstg.`recover_configuration_sub_component` (
  `ConfigurationID` int NOT NULL AUTO_INCREMENT,
  `FromDispositionID` int DEFAULT NULL,
  `workflow_id` int DEFAULT NULL,
  `parttypeid` int DEFAULT NULL,
  `input_id` int DEFAULT NULL,
  `ToDispositionID` int DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `FacilityID` int DEFAULT NULL,
  `DefaultValue` tinyint DEFAULT '0',
  `Status` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`ConfigurationID`)
) ENGINE=InnoDB AUTO_INCREMENT=0


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('155', 'Assets Recovered Configuration', 'AssetsRecoveredConfiguration', 'AssetsRecoveredConfiguration', '', '1', '', '1', '', '0', '11', '0');

UPDATE `awsdev`.`left_menus` SET `TabLink` = 'AssetsRecoveredConfigurationList' WHERE (`MenuID` = '1200');


ALTER TABLE `awsstg`.`asset` ADD COLUMN `InventoryType` VARCHAR(50) NULL DEFAULT NULL AFTER `idCustomertype`;

INSERT INTO `awsstg`.`asset_status` (`StatusID`, `Status`) VALUES ('14', 'Added to Serial');


ALTER TABLE `awsstg`.`asset`
ADD COLUMN `InventoryAddedToAssetScanID` BIGINT NULL DEFAULT NULL AFTER `InventoryType`,
ADD COLUMN `InventoryAddedToSerialNumber` VARCHAR(100) NULL DEFAULT NULL AFTER `InventoryAddedToAssetScanID`,
ADD COLUMN `InventoryAddedToOtherSerialDate` DATETIME NULL DEFAULT NULL AFTER `InventoryAddedToSerialNumber`;




CREATE TABLE `awsstg`.`recovery_type_recovery_complete_configuration` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `Recoverytypeid` INT NULL DEFAULT NULL,
  `idCustomertype` INT NULL DEFAULT NULL,
  `FacilityID` INT NULL DEFAULT NULL,
  `Status` VARCHAR(10) NULL DEFAULT NULL,
  `RecoveryCompleted` VARCHAR(5) NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`ID`));


CREATE TABLE `admin_file_uploads` (
   `UploaddataID` int NOT NULL AUTO_INCREMENT,
   `FileName` varchar(100) DEFAULT NULL,
   `DateCreated` varchar(45) DEFAULT NULL,
   `CreatedBy` int DEFAULT NULL,
   `FileType` varchar(100) DEFAULT NULL,
   PRIMARY KEY (`uploaddataID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb3

ALTER TABLE `awsdev`.`admin_file_uploads`
ADD COLUMN `Comments` TEXT NULL AFTER `FileType`;


-- Add Source Type and Material Type fields to business_rules table (Added on 2023-11-01)
-- In case AWSCustomerID, workflow_id, and part_types don't exist yet, add them first

ALTER TABLE `awsstg`.`business_rule` 
ADD COLUMN `AWSCustomerID` VARCHAR(5) NULL DEFAULT NULL AFTER `rule_id_text`,
ADD COLUMN `part_types` TEXT NULL DEFAULT NULL AFTER `AWSCustomerID`,
ADD COLUMN `idCustomertype` VARCHAR(5) NULL DEFAULT NULL AFTER `part_types`,
ADD COLUMN `MaterialType` VARCHAR(100) NULL DEFAULT NULL AFTER `idCustomertype`;




ALTER TABLE `awsstg`.`business_rule` 
DROP FOREIGN KEY `brttofacility`,
DROP FOREIGN KEY `brtoworkflow`,
DROP FOREIGN KEY `brtoinput`;
ALTER TABLE `awsstg`.`business_rule` 
DROP INDEX `brttofacility_idx` ,
DROP INDEX `brtoinput_idx` ,
DROP INDEX `brtoworkflow_idx` ;
; (Manually drop FK Index for facility, workflow, input_id)



ALTER TABLE `awsdev`.`business_rule` 
CHANGE COLUMN `workflow_id` `workflow_id` VARCHAR(5) NULL DEFAULT NULL ,
CHANGE COLUMN `input_id` `input_id` VARCHAR(150) NULL DEFAULT NULL ,
CHANGE COLUMN `FacilityID` `FacilityID` VARCHAR(5) NULL DEFAULT NULL ;


INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_id`, `attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('36', 'Region', 'Internal', 'Facility Region', 'RegionName', 'Active'); //Make sure the ID is 36


INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('36', 'US', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('36', 'EMEA', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('36', 'ASIA', 'Active');  //Make sure ID is 36


INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_id`, `attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('37', 'Second Arrival', 'Internal', 'If the part came for the second time', 'second_arrival', 'Active');
INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_id`, `attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('38', 'Third Arrival', 'Internal', 'If the part came for the thid time', 'third_arrival', 'Active'); // Make sure the ID are 37 and 38

INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('37', 'Yes', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('37', 'No', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('38', 'Yes', 'Active');
INSERT INTO `awsstg`.`business_rule_attribute_values` (`attribute_id`, `value`, `status`) VALUES ('38', 'No', 'Active');


INSERT INTO `awsstg`.`business_rule_attributes` (`attribute_id`, `attribute_name`, `attribute_type`, `attribute_description`, `field_name`, `status`) VALUES ('39', 'COO', 'Internal', 'COO ID from the input', 'COOID', 'Active'); // Make sure the ID is 39


ALTER TABLE `awsstg`.`business_rule_versions` ADD COLUMN `previous_version` TINYINT NULL DEFAULT 0 AFTER `status`;


ALTER TABLE `awsdev`.`business_rule` CHANGE COLUMN `input_id` `input_id` TEXT NULL DEFAULT NULL ;



ALTER TABLE `awsstg`.`bin_audit_records` 
ADD COLUMN `event_id` VARCHAR(50) NULL DEFAULT NULL AFTER `assigned_bin_scan_time`,
ADD COLUMN `batch_event_flag` VARCHAR(5) NULL DEFAULT NULL AFTER `event_id`,
ADD COLUMN `AWSCustomerID` INT NULL DEFAULT NULL AFTER `batch_event_flag`,
ADD COLUMN `WasteClassificationType` VARCHAR(5) NULL DEFAULT NULL AFTER `AWSCustomerID`;


ALTER TABLE `awsstg`.`bin_audit_records` 
ADD COLUMN `WasteCode` VARCHAR(50) NULL DEFAULT NULL AFTER `last_touched_by`;


CREATE TABLE `reassign_history` (
   `ReassignHistoryID` int NOT NULL AUTO_INCREMENT,
   `FromCustomPalletID` int DEFAULT NULL,
   `ToCustomPalletID` int DEFAULT NULL,
   `FromBinName` varchar(99) DEFAULT NULL,
   `ToBinName` varchar(99) DEFAULT NULL,
   `FromDispositionID` int DEFAULT NULL,
   `ToDispositionID` int DEFAULT NULL,
   `FromCOOID` varchar(99) DEFAULT NULL,
   `ToCOOID` varchar(99) DEFAULT NULL,
   `Fromparttypeid` varchar(99) DEFAULT NULL,
   `Toparttypeid` varchar(99) DEFAULT NULL,
   `FromMPN` varchar(99) DEFAULT NULL,
   `ToMPN` varchar(99) DEFAULT NULL,
   `ToShippingContainerID` varchar(99) DEFAULT NULL,
   `CreatedDate` datetime DEFAULT NULL,
   `CreatedBy` int DEFAULT NULL,
   `ReassignStatus` varchar(50) DEFAULT NULL,
   `FacilityID` int DEFAULT NULL,
   `origin_serial_scan_time` datetime DEFAULT NULL,
   `origin_mpn_scan_time` datetime DEFAULT NULL,
   `origin_parttype_scan_time` datetime DEFAULT NULL,
   `origin_coo_scan_time` datetime DEFAULT NULL,
   `origin_bin_scan_time` datetime DEFAULT NULL,
   `origin_disposition_scan_time` datetime DEFAULT NULL,
   `AssetScanID` bigint DEFAULT NULL,
   `ServerID` int DEFAULT NULL,
   `MediaID` int DEFAULT NULL,
   PRIMARY KEY (`ReassignHistoryID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci


CREATE TABLE `redisposition_history` (
   `ReDispositionHistoryID` int NOT NULL AUTO_INCREMENT,
   `FromCustomPalletID` int DEFAULT NULL,
   `ToCustomPalletID` int DEFAULT NULL,
   `FromBinName` varchar(99) DEFAULT NULL,
   `ToBinName` varchar(99) DEFAULT NULL,
   `FromDispositionID` int DEFAULT NULL,
   `ToDispositionID` int DEFAULT NULL,
   `FromCOOID` varchar(99) DEFAULT NULL,
   `ToCOOID` varchar(99) DEFAULT NULL,
   `Fromparttypeid` varchar(99) DEFAULT NULL,
   `Toparttypeid` varchar(99) DEFAULT NULL,
   `FromMPN` varchar(99) DEFAULT NULL,
   `ToMPN` varchar(99) DEFAULT NULL,
   `FromEvaluationID` int DEFAULT NULL,
   `ToEvaluationID` int DEFAULT NULL,
   `ToShippingContainerID` varchar(99) DEFAULT NULL,
   `CreatedDate` datetime DEFAULT NULL,
   `CreatedBy` int DEFAULT NULL,
   `ReDispositionStatus` varchar(50) DEFAULT NULL,
   `FacilityID` int DEFAULT NULL,
   `origin_serial_scan_time` datetime DEFAULT NULL,
   `origin_mpn_scan_time` datetime DEFAULT NULL,
   `origin_parttype_scan_time` datetime DEFAULT NULL,
   `origin_coo_scan_time` datetime DEFAULT NULL,
   `origin_bin_scan_time` datetime DEFAULT NULL,
   `origin_disposition_scan_time` datetime DEFAULT NULL,
   `AssetScanID` bigint DEFAULT NULL,
   `ServerID` int DEFAULT NULL,
   `MediaID` int DEFAULT NULL,
   PRIMARY KEY (`ReDispositionHistoryID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci


ALTER TABLE `awsstg`.`business_rule` CHANGE COLUMN `AWSCustomerID` `AWSCustomerID` VARCHAR(100) NULL DEFAULT NULL ;


INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `DashletName`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('159', 'Archived Rule List', 'ArchivedRulesList', 'ArchivedRulesList', '', '1', '', '1', '', '0', '3', '0');

//After BRE Release

ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN `ParentCustomPalletID` INT NULL DEFAULT NULL AFTER `PartTypeSummaryUpdatedBy`,
ADD COLUMN `ParentBinName` TEXT NULL DEFAULT NULL AFTER `ParentCustomPalletID`;




CREATE TABLE `awsstg`.`custompallet_tracking` (
  `TrackID` INT NOT NULL AUTO_INCREMENT,
  `CustomPalletID` INT NULL DEFAULT NULL,
  `BinName` TEXT NULL DEFAULT NULL,
  `Action` TEXT NULL DEFAULT NULL,
  `CreatedDate` DATETIME NULL DEFAULT NULL,
  `CreatedBy` INT NULL DEFAULT NULL,
  PRIMARY KEY (`TrackID`));




ALTER TABLE `awsstg`.`custompallet_tracking` 
ADD INDEX `custompallettrackingtocustompallet_idx` (`CustomPalletID` ASC) VISIBLE,
ADD INDEX `trackingtouser_idx` (`CreatedBy` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`custompallet_tracking` 
ADD CONSTRAINT `custompallettrackingtocustompallet`
  FOREIGN KEY (`CustomPalletID`)
  REFERENCES `awsstg`.`custompallet` (`CustomPalletID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `trackingtouser`
  FOREIGN KEY (`CreatedBy`)
  REFERENCES `awsstg`.`users` (`UserId`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN `SealID` VARCHAR(50) NULL DEFAULT NULL AFTER `ParentBinName`,
ADD COLUMN `ShippingControllerLoginID` VARCHAR(100) NULL DEFAULT NULL AFTER `SealID`,
ADD COLUMN `ContainerWeight` INT NULL DEFAULT NULL AFTER `ShippingControllerLoginID`,
ADD COLUMN `RecentSealDate` DATETIME NULL DEFAULT NULL AFTER `ContainerWeight`,
ADD COLUMN `RecentSealBy` INT NULL DEFAULT NULL AFTER `RecentSealDate`;



ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN `ReferenceID` VARCHAR(500) NULL DEFAULT NULL AFTER `RecentSealBy`,
ADD COLUMN `ReferenceTypeID` INT NULL DEFAULT NULL AFTER `ReferenceID`,
ADD COLUMN `ReferenceType` VARCHAR(100) NULL DEFAULT NULL AFTER `ReferenceTypeID`,
ADD COLUMN `ReferenceIDRequired` TINYINT NULL DEFAULT 0 AFTER `ReferenceType`;



ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN `CustomerLock` TINYINT NULL DEFAULT 0 AFTER `ReferenceIDRequired`,
ADD COLUMN `AWSCustomerID` INT NULL DEFAULT NULL AFTER `CustomerLock`;


ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN `GroupID` INT NULL DEFAULT NULL AFTER `AWSCustomerID`;


ALTER TABLE `awsstg`.`reassign_history` 
ADD COLUMN `FromSerialNumber` VARCHAR(99) NULL DEFAULT NULL AFTER `MediaID`,
ADD COLUMN `ToSerialNumber` VARCHAR(99) NULL DEFAULT NULL AFTER `FromSerialNumber`;

ALTER TABLE `awsstg`.`reassign_history` 
ADD COLUMN `NotesRequired` VARCHAR(250) NULL DEFAULT NULL AFTER `ToSerialNumber`;


ALTER TABLE `awsstg`.`reassign_history` 
ADD COLUMN `old_serial_scan_time` DATETIME NULL DEFAULT NULL AFTER `NotesRequired`,
ADD COLUMN `DispositionOverrideReason` VARCHAR(250) NULL DEFAULT NULL AFTER `old_serial_scan_time`,
ADD COLUMN `origin_facility_scan_time` DATETIME NULL DEFAULT NULL AFTER `DispositionOverrideReason`,
ADD COLUMN `ToFacilityID` INT NULL DEFAULT NULL AFTER `origin_facility_scan_time`;



ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN `idPackage` INT NULL DEFAULT NULL AFTER `GroupID`;


ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN ` MaxLimitRequired` TINYINT NULL DEFAULT 0 AFTER `idPackage`;


INSERT INTO `awsstg`.`custompallet_status` (`StatusID`, `Status`, `StatusDescription`) VALUES ('5', 'Deleted', 'Deleted');


ALTER TABLE `awsstg`.`package` ADD COLUMN `PrefixRequiredForBinName` TINYINT NULL DEFAULT 0 AFTER `WasteClassificationType`;


ALTER TABLE `awsstg`.`parts_recovery_wip_records` ADD COLUMN `AWSCustomerID` INT NULL DEFAULT NULL AFTER `first_disposition_id`;


ALTER TABLE `awsstg`.`speed_server_recovery` 
ADD COLUMN `AWSCustomerID` INT NULL DEFAULT NULL AFTER `first_disposition_id`;



ALTER TABLE `awsstg`.`speed_media_recovery` 
ADD COLUMN `AWSCustomerID` INT NULL DEFAULT NULL AFTER `first_disposition_id`;



ALTER TABLE `awsstg`.`custompallet_tracking` ADD COLUMN `ModuleName` VARCHAR(150) NULL DEFAULT NULL AFTER `CreatedBy`;

INSERT INTO `awsstg`.`custompallet_status` (`StatusID`, `Status`, `StatusDescription`) VALUES ('6', 'Added to Shipment', 'Added to Shipment');
INSERT INTO `awsstg`.`custompallet_status` (`StatusID`, `Status`, `StatusDescription`) VALUES ('7', 'Shipped', 'Shipped'); // Make sure the ids are 6 and 7


ALTER TABLE `awsstg`.`custompallet` ADD COLUMN `ShippingID` VARCHAR(50) NULL DEFAULT NULL AFTER `MaxLimitRequired`;


ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN `bin_added_to_shipment_time` DATETIME NULL DEFAULT NULL AFTER `ShippingID`,
ADD COLUMN `bin_added_to_shipment_by` INT NULL DEFAULT NULL AFTER `bin_added_to_shipment_time`;

ALTER TABLE `awsstg`.`custompallet` ADD COLUMN `ASNBin` TINYINT NULL DEFAULT 0 AFTER `bin_added_to_shipment_by`;


ALTER TABLE `awsstg`.`custompallet` ADD COLUMN `byproduct_id` INT NULL DEFAULT NULL AFTER `ASNBin`;


ALTER TABLE `awsstg`.`shipping_container_serials` 
ADD COLUMN `CustomPalletID` INT NULL DEFAULT NULL AFTER `COOID`,
ADD COLUMN `BinName` TEXT NULL DEFAULT NULL AFTER `CustomPalletID`;

ALTER TABLE `awsstg`.`custompallet_items` ADD COLUMN `byproduct_id` INT NULL DEFAULT NULL AFTER `Quantity`;

ALTER TABLE `awsstg`.`pallets` ADD COLUMN `OriginalPalletID` VARCHAR(50) NULL DEFAULT NULL AFTER `FacilityTransferContainer`;

ALTER TABLE `awsstg`.`custompallet` ADD COLUMN `OriginalPalletID` VARCHAR(50) NULL DEFAULT NULL AFTER `byproduct_id`;

ALTER TABLE `awsstg`.`custompallet` ADD COLUMN `ASNContainer` TINYINT NULL DEFAULT 0 AFTER `OriginalPalletID`;

ALTER TABLE `awsstg`.`custompallet` ADD COLUMN `idPallet` VARCHAR(50) NULL DEFAULT NULL AFTER `ASNContainer`;

ALTER TABLE `awsstg`.`custompallet` ADD COLUMN `BatchRecovery` TINYINT NULL DEFAULT 0 AFTER `idPallet`;

New link for Shipment Prep page, Shipment Container, Pending OUtbound container pages are inactive


update awsstg.speed_media_recovery a,awsstg.pallets p 
set a.AWSCustomerID = p.AWSCustomerID where a.idPallet = p.idPallet and isnull(a.AWSCustomerID); (This is for updating AWSCustomerID for media)
Use similar queries for asset and server tables


truncate table awsstg.station_container_mapping; (this is for mapping unserialized serials to Shipping containers, since we removed shipping containers, this records should be deleted)

ALTER TABLE `awsdev`.`package` ADD COLUMN `PrefixValue` VARCHAR(10) NULL DEFAULT NULL AFTER `PrefixRequiredForBinName`;

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Archive Bins List', 'ArchiveBinsList', 'ArchiveBinsList', '1', '1', 'eViridis Administration', '0', '43', '0');


ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN `DeletedDate` DATETIME NULL DEFAULT NULL AFTER `BatchRecovery`,
ADD COLUMN `DeletedBy` INT NULL DEFAULT NULL AFTER `DeletedDate`;


ALTER TABLE `awsstg`.`custompallet` ADD COLUMN `RemovalCode` TEXT NULL DEFAULT NULL AFTER `DeletedBy`;


CREATE TABLE `awsdev`.`ParkType` (
  `ParkTypeID` INT NOT NULL AUTO_INCREMENT,
  `FacilityID` INT NULL,
  `ParkTypeName` VARCHAR(99) NULL,
  `Description` VARCHAR(250) NULL,
  `Status` INT NULL,
  PRIMARY KEY (`ParkTypeID`),
  UNIQUE INDEX `ParkTypeID_UNIQUE` (`ParkTypeID` ASC) VISIBLE);

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Park Type Configuration', 'ParkTypeConfiguration', 'ParkTypeConfiguration', '1', '1', 'eViridis Administration', '0', '90', '0');

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Truck Type Configuration', 'TruckTypeConfiguration', 'TruckTypeConfiguration', '1', '1', 'eViridis Administration', '0', '91', '0');

CREATE TABLE `awsdev`.`TruckType` (
  `TruckTypeID` INT NOT NULL AUTO_INCREMENT,
  `FacilityID` INT NULL,
  `TruckTypeName` VARCHAR(99) NULL,
  `Description` VARCHAR(250) NULL,
  `Status` INT NULL,
  PRIMARY KEY (`TruckTypeID`),
  UNIQUE INDEX `TruckTypeID_UNIQUE` (`TruckTypeID` ASC) VISIBLE);

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'TDR SOP', 'tdrsopconfiguration', 'tdrsopconfiguration', '1', '1', 'eViridis Administration', '0', '92', '0');

CREATE TABLE `awsdev`.`TDRSOP` (
  `TDRSOPID` INT NOT NULL AUTO_INCREMENT,
  `TdrType` VARCHAR(99) NULL,
  `StepNo` INT NULL,
  `Description` VARCHAR(250) NULL,
  `Status` INT NULL,
  PRIMARY KEY (`TDRSOPID`),
  UNIQUE INDEX `TDRSOPID_UNIQUE` (`TDRSOPID` ASC) VISIBLE);

CREATE TABLE `awsdev`.`ParkingLocation` (
  `ParkingLocationID` INT NOT NULL AUTO_INCREMENT,
  `FacilityID` INT NULL,
  `ParkTypeID` INT NULL,
  `ParkingLocationName` VARCHAR(99) NULL,
  `Description` VARCHAR(250) NULL,
  `Status` INT NULL,
  PRIMARY KEY (`ParkingLocationID`),
  UNIQUE INDEX `ParkingLocationID_UNIQUE` (`ParkingLocationID` ASC) VISIBLE);


INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `GroupName`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('153', 'Parking Location', 'ParkingLocation', 'ParkingLocation', '1', '1', 'eViridis Administration', '0', '93', '0');


ALTER TABLE `awsdev`.`users` ADD COLUMN `BinBulkUploadEligible` TINYINT NULL DEFAULT 0 AFTER `BulkRecoveryController`;

ALTER TABLE `awsdev`.`Carrier` 
ADD COLUMN `Description` VARCHAR(250) NULL DEFAULT NULL AFTER `UpdatedBy`,
ADD COLUMN `Address` VARCHAR(250) NULL DEFAULT NULL AFTER `Description`,
ADD COLUMN `POC` VARCHAR(50) NULL DEFAULT NULL AFTER `Address`,
ADD COLUMN `ContactEmail` VARCHAR(45) NULL DEFAULT NULL AFTER `POC`,
ADD COLUMN `Phone` VARCHAR(45) NULL DEFAULT NULL AFTER `ContactEmail`,
ADD COLUMN `WasteCollectionEligible` INT NULL DEFAULT NULL AFTER `Phone`;

ALTER TABLE `awsdev`.`Carrier` 
ADD COLUMN `WasteCollectionPermit` VARCHAR(45) NULL DEFAULT NULL AFTER `WasteCollectionEligible`;

ALTER TABLE `awsdev`.`custompallet` ADD COLUMN `ConvertedFromShippingContainer` TINYINT NULL DEFAULT 0 AFTER `RemovalCode`;

ALTER TABLE `awsstg`.`shipping_container_serials` 
ADD INDEX `shippingserialtoCustomPalletID_idx` (`CustomPalletID` ASC) VISIBLE;
;
ALTER TABLE `awsstg`.`shipping_container_serials` 
ADD CONSTRAINT `shippingserialtoCustomPalletID`
  FOREIGN KEY (`CustomPalletID`)
  REFERENCES `awsstg`.`custompallet` (`CustomPalletID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


  ALTER TABLE `awsstg`.`custompallet` 
ADD COLUMN `InspectionResult` VARCHAR(100) NULL DEFAULT NULL AFTER `ConvertedFromShippingContainer`,
ADD COLUMN `InspectionAuditDateTime` DATETIME NULL DEFAULT NULL AFTER `InspectionResult`,
ADD COLUMN `InspectionAuditBy` INT NULL DEFAULT NULL AFTER `InspectionAuditDateTime`;


INSERT INTO `awsstg`.`custompallet_status` (`StatusID`, `Status`, `StatusDescription`) VALUES ('8', 'Quarantine', 'Quarantine');

///////////////After Release Aug 11th After DB Data//////////////////////

ALTER TABLE `awsstg`.`TDRSOP` 
ADD COLUMN `VehicleType` VARCHAR(250) NULL DEFAULT NULL AFTER `Status`;

CREATE TABLE `Truck` (
   `TruckID` int NOT NULL AUTO_INCREMENT,
   `FacilityID` int DEFAULT NULL,
   `ArrivalType` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `ParkingLocationID` int DEFAULT NULL,
   `CarrierID` int DEFAULT NULL,
   `ArrivalDate` date DEFAULT NULL,
   `ArrivalTime` time DEFAULT NULL,
   `LoadType` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `TruckTypeID` int DEFAULT NULL,
   `TruckReg` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `DriverName` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `DriverID` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `ShipmentTicketID` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `ClassificationType` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `WasteCollectionPermit` varchar(250) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `Notes` varchar(250) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `Status` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `CreatedDate` datetime DEFAULT NULL,
   `CreatedBy` int DEFAULT NULL,
   `UpdatedDate` datetime DEFAULT NULL,
   `UpdatedBy` int DEFAULT NULL,
   `LoadNumber` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
   `TrailerNumber` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
   PRIMARY KEY (`TruckID`),
   UNIQUE KEY `TruckID_UNIQUE` (`TruckID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
 
 INSERT INTO `awsstg`.`tabs` (`TabID`, `TabName`, `TabLink`, `Description`, `AccountID`, `Status`, `Dashboard`, `Order`, `TabIcon`) VALUES ('169', 'Truckyard/TDR', 'Truckyard', 'Truckyard', '1', '1', '0', '20','local_shipping');

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('169', 'Truck Booking List', 'TruckList', 'TruckList', '1', '1', '0', '2', '0');

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('169', 'Truck Booking', 'Truck', 'Truck', '1', '1', '0', '1', '0');

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('169', 'Truck Booking Calender', 'TruckBookingCalender', 'TruckBookingCalender', '1', '1', '0', '3', '0');

INSERT INTO `awsstg`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('169', 'Trailer Dock/Release', 'TrailerDockRelease', 'TrailerDockRelease', '1', '1', '0', '4', '0');

in the left menus, rename Dispoisition Override Container to Disposition Override Bin


create new field Notes in custompallet_items table




ALTER TABLE `awsdev`.`bin_consolidation_asset_movement` 
ADD COLUMN `UnserializedRecoveryRecordID` INT NULL DEFAULT NULL AFTER `batch_event_flag`;


INSERT INTO `awsdev`.`tabs` (`TabID`, `TabName`, `TabLink`, `Description`, `AccountID`, `Status`, `Dashboard`, `Order`, `TabIcon`) VALUES ('170', 'Truckyard', 'Truck', 'Truck', '1', '1', '0', '21', 'local_shipping');

INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('170', 'Truck Booking', 'TruckBooking', 'TruckBooking', '1', '1', '0', '1', '0');
INSERT INTO `awsdev`.`left_menus` (`TabID`, `TabName`, `TabLink`, `Description`, `Image`, `Status`, `AccountID`, `CustomFields`, `Order`, `ReadonlyEnabled`) VALUES ('170', 'Truck Form', 'TruckForm', 'TruckForm', '', '1', '1', '0', '2', '0');

ALTER TABLE `awsdev`.`TDRSOP` 
CHANGE COLUMN `Status` `Status` VARCHAR(10) NULL DEFAULT NULL ;

ALTER TABLE `awsdev`.`TruckType` 
CHANGE COLUMN `Status` `Status` VARCHAR(10) NULL DEFAULT NULL ;