<?php
session_start();
include_once("admin.class.php");
include_once("../../common_functions.php");
include_once("../../excel_reader/SimpleXLSX.php");
include_once("../../Truckyard/templates/xlsxwriter.class.php");
include_once("../../Truckyard/templates/xlsxwriterplus.class.php");
use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;

class TruckClass extends CommonClass
{
 
	public function GetParkingLocations($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
		/*	if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Workflow')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Workflow Page';
				return json_encode($json);
			}*/
			$query = "select * from ParkingLocation where Status = '1' order by `ParkingLocationName`";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result =  array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Parking Location Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetCarriers($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
		/*	if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Workflow')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Workflow Page';
				return json_encode($json);
			}*/
			$query = "select * from Carrier where StatusID = '1' order by `CarrierName`";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result =  array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Carrier Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetTruckTypes($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
		/*	if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Workflow')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Workflow Page';
				return json_encode($json);
			}*/
			$query = "select * from TruckType where Status = '1' order by `TruckTypeName`";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result =  array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No TruckType Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
	public function TruckSave($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$chkdt = $data['ArrivalDate'];
		$chkdtarr=explode("GMT",$chkdt);
		$newdt= strtotime($chkdtarr[0]);
		$data['ArrivalDate'] = date("Y-m-d",$newdt);

		// Arrival Time
		$chkdt = $data['ArrivalTime'];
		$chkdtarr = explode("GMT", $chkdt);
		$newdt = strtotime($chkdtarr[0]);
		$data['ArrivalTime'] = date("H:i:s", $newdt); // <-- Just time

		// Departure Time
		/*$chkdt = $data['DepartureTime'];
		$chkdtarr = explode("GMT", $chkdt);
		$newdt = strtotime($chkdtarr[0]);
		$data['DepartureTime'] = date("H:i:s", $newdt); // <-- Just time*/


		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/
		//return json_encode($json);
		if ($data['TruckID'] == '') { //If New Class
			$query = "insert into Truck (FacilityID,ArrivalType,ParkingLocationID,CarrierID,ArrivalDate,ArrivalTime,LoadType,LoadNumber,TruckTypeID,TruckReg,TrailerNumber,DriverName,DriverID,ShipmentTicketID,ClassificationType,WasteCollectionPermit,Notes,Status,CreatedDate,CreatedBy) values ('" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['CarrierID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalDate']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalTime']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['LoadType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['LoadNumber']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TruckReg']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TrailerNumber']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['DriverName']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['DriverID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ShipmentTicketID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ClassificationType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['WasteCollectionPermit']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Notes']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',NOW(),'" . $_SESSION['user']['UserId'] . "')";
		} else {
			$query = "update Truck set FacilityID='" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "',ArrivalType='" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalType']) . "',ParkingLocationID='" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "',CarrierID='" . mysqli_real_escape_string($this->connectionlink, $data['CarrierID']) . "',ArrivalDate='" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalDate']) . "',ArrivalTime='" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalTime']) . "',LoadType='" . mysqli_real_escape_string($this->connectionlink, $data['LoadType']) . "',LoadNumber='" . mysqli_real_escape_string($this->connectionlink, $data['LoadNumber']) . "',TruckTypeID='" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "',TruckReg='" . mysqli_real_escape_string($this->connectionlink, $data['TruckReg']) . "',TrailerNumber='" . mysqli_real_escape_string($this->connectionlink, $data['TrailerNumber']) . "',DriverName='" . mysqli_real_escape_string($this->connectionlink, $data['DriverName']) . "',DriverID='" . mysqli_real_escape_string($this->connectionlink, $data['DriverID']) . "',ShipmentTicketID='" . mysqli_real_escape_string($this->connectionlink, $data['ShipmentTicketID']) . "',ClassificationType='" . mysqli_real_escape_string($this->connectionlink, $data['ClassificationType']) . "',WasteCollectionPermit='" . mysqli_real_escape_string($this->connectionlink, $data['WasteCollectionPermit']) . "',Notes='" . mysqli_real_escape_string($this->connectionlink, $data['Notes']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',UpdatedDate =NOW(),UpdatedBy='" . $_SESSION['user']['UserId'] . "' where TruckID='" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";
		}
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		if ($data['TruckID'] == '') {
			$insert_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = "New Truck created";
			$json['TruckID'] = $insert_id;

		} else {
			$json['Success'] = true;
			$json['Result'] = "Truck modified";
		}
		return json_encode($json);
	}

	public function GetTruckDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/

		//return json_encode($json);
		$query = "select * from Truck where TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "' ORDER BY TruckID";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Truck ID";
		}
		return json_encode($json);
	}

	public function GetTruckList($data)
	{
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['TruckID']
			);

			$query = "select T.*,PL.ParkingLocationName,F.FacilityName,C.CarrierName from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID left join facility F on T.FacilityID = F.FacilityID left join Carrier C on T.CarrierID = C.CarrierID where 1";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'FacilityName') {
							$query = $query . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'CarrierName') {
							$query = $query . " AND C.CarrierName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ParkingLocationName') {
							$query = $query . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalType') {
							$query = $query . " AND T.ArrivalType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalDate') {
							$query = $query . " AND T.ArrivalDate like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalTime') {
							$query = $query . " AND T.ArrivalTime like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadType') {
							$query = $query . " AND T.LoadType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'TruckReg') {
							$query = $query . " AND T.TruckReg like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'DriverName') {
							$query = $query . " AND T.DriverName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'DriverID') {
							$query = $query . " AND T.DriverID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Notes') {
							$query = $query . " AND T.Notes like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadNumber') {
							$query = $query . " AND T.LoadNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'TrailerNumber') {
							$query = $query . " AND T.TrailerNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Status') {
							$query = $query . " AND T.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}

					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by F.FacilityName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'CarrierName') {
					$query = $query . " order by C.CarrierName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ParkingLocationName') {
					$query = $query . " order by PL.ParkingLocationName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalType') {
					$query = $query . " order by T.ArrivalType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalDate') {
					$query = $query . " order by T.ArrivalDate " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalTime') {
					$query = $query . " order by T.ArrivalTime " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadType') {
					$query = $query . " order by T.LoadType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'TruckReg') {
					$query = $query . " order by T.TruckReg " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'DriverName') {
					$query = $query . " order by T.DriverName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'DriverID') {
					$query = $query . " order by T.DriverID " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Notes') {
					$query = $query . " order by T.Notes " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadNumber') {
					$query = $query . " order by T.LoadNumber " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'TrailerNumber') {
					$query = $query . " order by T.TrailerNumber " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Status') {
					$query = $query . " order by T.Status " . $order_by_type . " ";
				} 
 			} else {
				$query = $query . " order by T.TruckID desc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					 // Calculate DepartureTime: 30 mins after ArrivalTime
            if (!empty($row['ArrivalTime'])) {
                $arrival = new DateTime($row['ArrivalTime']);
                $arrival->modify('+30 minutes');
                $row['DepartureTime'] = $arrival->format('H:i');
            } else {
                $row['DepartureTime'] = null;
            }
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Data Available";
			}

			if ($data['skip'] == 0) {

				$query1 = "select count(*) from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID 
								left join facility F on T.FacilityID = F.FacilityID	left join Carrier C on T.CarrierID = C.CarrierID where 1";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'FacilityName') {
								$query1 = $query1 . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'CarrierName') {
								$query1 = $query1 . " AND C.CarrierName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ParkingLocationName') {
								$query1 = $query1 . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalType') {
								$query1 = $query1 . " AND T.ArrivalType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalDate') {
								$query1 = $query1 . " AND T.ArrivalDate like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalTime') {
								$query1 = $query1 . " AND T.ArrivalTime like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadType') {
								$query1 = $query1 . " AND T.LoadType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'TruckReg') {
								$query1 = $query1 . " AND T.TruckReg like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'DriverName') {
								$query1 = $query1 . " AND T.DriverName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'DriverID') {
								$query1 = $query1 . " AND T.DriverID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Notes') {
								$query1 = $query1 . " AND T.Notes like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadNumber') {
								$query1 = $query1 . " AND T.LoadNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'TrailerNumber') {
								$query1 = $query1 . " AND T.TrailerNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Status') {
								$query1 = $query1 . " AND T.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}

						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function DeleteTruck($data)
		{
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);

			$transaction = 'Administration ---> eViridis Administration --->Truck Booking';
			$description = 'Truck Booking Delete';
			$this->RecordUserTransaction($transaction, $description);
			$sql = "DELETE FROM Truck WHERE TruckID = '".$data['id']."'";
			$query = mysqli_query($this->connectionlink,$sql);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			else
			{
				$json['Success'] = true;
				$json['Result'] = "Record Deleted Successfully.";
				return json_encode($json);
			}
		}
	public function ChangeStatus($data) {
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);
		$query = "update Truck set Status='".$data['Status']."',UpdatedDate=NOW(),UpdatedBy='".$_SESSION['user']['UserId']."' where TruckID='".$data['TruckID']."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		} else {
			$json['Success'] = true;
			$json['Result'] = 'Status Changed';
			return json_encode($json);
		}
	}

	public function GenerateTruckListxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['TruckListxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

	public function UploadTruckBookingFile($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Truck Booking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Truck Booking Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Truck Booking')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Truck Booking Page';
				return json_encode($json);
			}
			if($data['file']['type'] != 'application/vnd.ms-excel' && $data['file']['type'] != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
				$json['Success'] = false;
				$json['Result'] = 'Invalid File type';
				return json_encode($json);
			}
			$filename = time().$data['file']['name'];
			//$target_path = '../../uploads/'.$filename;

			$upload = $this->UploadToS3($filename,$data['file']['tmp_name']);
			if($upload) {
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Unable to upload file';
				$json['Upload'] = $upload;
				return json_encode($json);
			}

			//if(move_uploaded_file($data['file']['tmp_name'], $target_path)) {
			if(true) {

				$query = "insert into admin_file_uploads (FileName,DateCreated,CreatedBy,FileType) values ('".$filename."',NOW(), '".$_SESSION['user']['UserId']."','TruckBooking')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$UploadID = mysqli_insert_id($this->connectionlink);

				$s3 = S3Client::factory(
					array(
						'credentials' => array(
							'key' => S3_key_eviridis,
							'secret' => S3_secret_eviridis
						),
						'version' => 'latest',
						'region'  => S3_region_eviridis
					)
				);
				$s3->registerStreamWrapper();

				//if($xlsx = SimpleXLSX::parse('../../uploads/'.$filename)) {
				if($xlsx = SimpleXLSX::parse('s3://'.S3_bucket_eviridis.'/'.$filename)) {
					$can_insert = false;
					$assets_count = 0;
					$i = 0;
					$a = 1;
					$error_message = '';
					$new_records = 0;
					$updated_records = 0;
					$final_output = array();
					$Header_output = ['Facility','Arrival Type','Parking Location','Carrier','Arrival Date','Expected Arrival Time','Load Type','Load Number','Vehicle Type','Truck Reg','Trailer Number','Driver','DriverID','Shipment Ticket ID','Classification Type','Waste Collection Permit','Notes','Status'];
					foreach ($xlsx->rows() as $elt) {// Looking full excel for validation
						$current_row = $elt;
						if($a == 1) { // Executes only for first row
							if(trim($elt[0]) != 'Facility') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell A1 Should be 'Facility'";
								return json_encode($json);
							}

							if(trim($elt[1]) != 'Arrival Type') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell B1 Should be 'Arrival Type'";
								return json_encode($json);
							}

							if(trim($elt[2]) != 'Parking Location') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell C1 Should be 'Parking Location'";
								return json_encode($json);
							}

							if(trim($elt[3]) != 'Carrier') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell D1 Should be 'Carrier'";
								return json_encode($json);
							}

							if(trim($elt[4]) != 'Arrival Date') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell E1 Should be 'Arrival Date'";
								return json_encode($json);
							}	
							if(trim($elt[5]) != 'Expected Arrival Time') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell F1 Should be 'Expected Arrival Time'";
								return json_encode($json);
							}	
							if(trim($elt[6]) != 'Load Type') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell G1 Should be 'Load Type'";
								return json_encode($json);
							}	
							if(trim($elt[7]) != 'Load Number') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell H1 Should be 'Load Number'";
								return json_encode($json);
							}	
							if(trim($elt[8]) != 'Vehicle Type') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell I1 Should be 'Vehicle Type'";
								return json_encode($json);
							}	
							if(trim($elt[9]) != 'Truck Reg') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell J1 Should be 'Truck Reg'";
								return json_encode($json);
							}	
							if(trim($elt[10]) != 'Trailer Number') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell K1 Should be 'Trailer Number'";
								return json_encode($json);
							}	
							if(trim($elt[11]) != 'Driver') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell L1 Should be 'Driver'";
								return json_encode($json);
							}	
							if(trim($elt[12]) != 'DriverID') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell M1 Should be 'DriverID'";
								return json_encode($json);
							}	
							if(trim($elt[13]) != 'Shipment Ticket ID') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell N1 Should be 'Shipment Ticket ID'";
								return json_encode($json);
							}	
							if(trim($elt[14]) != 'Classification Type') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell O1 Should be 'Classification Type'";
								return json_encode($json);
							}	
							if(trim($elt[15]) != 'Waste Collection Permit') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell P1 Should be 'Waste Collection Permit'";
								return json_encode($json);
							}	
							if(trim($elt[16]) != 'Notes') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell Q1 Should be 'Notes'";
								return json_encode($json);
							}	
							if(trim($elt[17]) != 'Status') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell R1 Should be 'Status'";
								return json_encode($json);
							}							
						}

						if($a > 1) {//looping through data
							//if(trim($elt[1]) != '' && trim($elt[2]) != '' && trim($elt[3]) != '' && trim($elt[4]) != '') {
								$exception = false;
								$exception_message = '';
								$exception_field = '';

									
								if(trim($elt[0]) != '') { //SiteID
									//Start check If SiteID exists in our database
									$query101 = "select * from facility where FacilityName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."' and FacilityStatus = '1'";
									$q101 = mysqli_query($this->connectionlink,$query101);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row101 = mysqli_fetch_assoc($q101);
										$FacilityID = $row101['FacilityID'];
									} else {
										$exception = true;
										$exception_message = 'Facility Name does not exist';
										$exception_field = 'FacilityID';
										$FacilityID = NULL;
										$FacilityID = '';
										$current_row[] = 'Failure';
										$current_row[] = $exception_message;
										$final_output[] = $current_row;
										$a++;
										continue;
									}
									//End check If SiteID exists in our database								
								} else {
									$exception = true;
									$exception_message = 'Missing Facility Name';
									$exception_field = 'FacilityID';
									$FacilityID = NULL;

									$current_row[] = 'Failure';
									$current_row[] = $exception_message;
									$final_output[] = $current_row;
									$a++;
									continue;
								}

								if(trim($elt[3]) != '') { //SiteID
									//Start check If SiteID exists in our database
									$query101 = "select * from ParkType where ParkTypeName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."' and Status = '1'";
									$q101 = mysqli_query($this->connectionlink,$query101);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row101 = mysqli_fetch_assoc($q101);
										$ParkTypeID = $row101['ParkTypeID'];
									} else {
										$exception = true;
										$exception_message = 'ParkType Name does not exist';
										$exception_field = 'ParkTypeID';
										$ParkTypeID = NULL;
										$ParkTypeID = '';
										$current_row[] = 'Failure';
										$current_row[] = $exception_message;
										$final_output[] = $current_row;
										$a++;
										continue;
									}
									//End check If SiteID exists in our database								
								} else {
									$exception = true;
									$exception_message = 'Missing ParkType Name';
									$exception_field = 'ParkTypeID';
									$ParkTypeID = NULL;

									$current_row[] = 'Failure';
									$current_row[] = $exception_message;
									$final_output[] = $current_row;
									$a++;
									continue;
								}

								if(trim($elt[4]) != '') { //SiteID
									//Start check If SiteID exists in our database
									$query101 = "select * from statusses where StatusName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."'";
									$q101 = mysqli_query($this->connectionlink,$query101);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										while ($row101 = mysqli_fetch_assoc($q101)) {
											if ($row101['StatusID'] == 'Active') {
												$row101['StatusID'] = '1';
											} else if ($row101['StatusID'] == 'InActive') {
												$row101['StatusID'] = '2';
											}
											$StatusID = $row101['StatusID'];
										}
										$json['Success'] = true;
										//$json['Result'] = $StatusID;
									} else {
										$exception = true;
										$exception_message = 'Status Name does not exist';
										$exception_field = 'StatusID';
										$StatusID = NULL;

										$current_row[] = 'Failure';
										$current_row[] = $exception_message;
										$final_output[] = $current_row;
										$a++;
										continue;
									}
									//End check If SiteID exists in our database								
								} else {
									$exception = true;
									$exception_message = 'Missing Status Name';
									$exception_field = 'Status';
									$StatusID = NULL;

									$current_row[] = 'Failure';
									$current_row[] = $exception_message;
									$final_output[] = $current_row;
									$a++;
									continue;
								}

								if (!isset($elt[1]) || trim($elt[1]) === '') {
								    // Description missing, raise failure
								    $exception = true;
								    $exception_message = 'Parking Location Name is Missing';
								    $exception_field = 'ParkingLocationName';
								    $elt[1] = NULL;

								    $current_row[] = 'Failure';
								    $current_row[] = $exception_message;
								    $final_output[] = $current_row;
								    $a++;
								    continue;
								}

								if (!isset($elt[2]) || trim($elt[2]) === '') {
								    // Description missing, raise failure
								    $exception = true;
								    $exception_message = 'Description is Missing';
								    $exception_field = 'Description';
								    $elt[1] = NULL;

								    $current_row[] = 'Failure';
								    $current_row[] = $exception_message;
								    $final_output[] = $current_row;
								    $a++;
								    continue;
								}

								//Start Check If Rigname Exists
								$query2 = "select ParkingLocationID from ParkingLocation where ParkingLocationName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$FacilityID)."' ";
								$q2 = mysqli_query($this->connectionlink,$query2);
								if(mysqli_affected_rows($this->connectionlink) > 0) {
									$row2 = mysqli_fetch_assoc($q2);
									$ID = $row2['ParkingLocationID'];
								} else {//Create new Rig
									$ID = 0;
								}
								//End Chedk If Rigname Exists

								if($ID > 0) {								
									$query = "update ParkingLocation set ParkingLocationName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."',FacilityID = '".mysqli_real_escape_string($this->connectionlink,$FacilityID)."',Description = '".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."',ParkTypeID = '".mysqli_real_escape_string($this->connectionlink,trim($ParkTypeID))."',Status = '".mysqli_real_escape_string($this->connectionlink,trim($StatusID))."' where ParkingLocationID = '".$ID."'";
									
									$q = mysqli_query($this->connectionlink,$query);
									if(mysqli_error($this->connectionlink)) {
										$error_message = $error_message . 'Error at Row '.$a.'  '.mysqli_error($this->connectionlink).'';
									} else {
										$updated_records = $updated_records + 1;
									}


									$current_row[] = 'Success';
									$current_row[] = 'Record Updated';
									$final_output[] = $current_row;									
								} else {//Create new MPN
									//Start check duplicate
									$query11 = "select count(*) from ParkingLocation where ParkingLocationName =  '".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$FacilityID)."' ";
									$q11 = mysqli_query($this->connectionlink,$query11);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row11 = mysqli_fetch_assoc($q11);
										if($row11['count(*)'] > 0) {
											$json['Success'] = false;
											$json['Result'] = 'Parking Location Name already Exists for the Facility';
											//return json_encode($json);


											$current_row[] = 'Failure';
											$current_row[] = 'Parking Location Name already exists for the Facility';
											$final_output[] = $current_row;
											$a++;
											continue;
										}
									} else {
										$json['Success'] = false;
										$json['Result'] = 'Invalid';
										return json_encode($json);
									}
									//End check duplicate
									$query1 = "insert into ParkingLocation (FacilityID,ParkingLocationName,Description,ParkTypeID,Status) values ('".mysqli_real_escape_string($this->connectionlink,$FacilityID)."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,$ParkTypeID)."','".mysqli_real_escape_string($this->connectionlink,$StatusID)."')";
									$q1 = mysqli_query($this->connectionlink,$query1);
									if(mysqli_error($this->connectionlink)) {
										$error_message = $error_message . 'Error at Row '.$a.'  '.mysqli_error($this->connectionlink).'';
									} else {
										$new_records = $new_records + 1;
									}

									$current_row[] = 'Success';
									$current_row[] = 'Record Created';
									$final_output[] = $current_row;
									
								}
							//}
						}
						$a++;
					}
				}
				else {
					$json['Success'] = false;
					$json['Result'] = SimpleXLSX::parseError();
					return json_encode($json);
				}

				//Start Update Upload record
				$message = $new_records.' New Truck Booking Created, '.$updated_records.'  Truck Booking Updated. '.$error_message;
				$query4 = "update admin_file_uploads set Comments = '".mysqli_real_escape_string($this->connectionlink,$message)."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				//End Update Upload record

				$transaction = 'Truckyard --->  Truck Booking';
				$description = 'Truck Truck File Uploaded';
				$this->RecordUserTransaction($transaction,$description);

				$json['Success'] = true;
				$json['Result'] = $new_records.' New Truck Booking Created, '.$updated_records.'  Truck Booking Updated. '.$error_message;
				$json['FinalResult'] = $final_output;
				$_SESSION['FinalResult'] = $final_output;
				$_SESSION['HeaderResult'] = $Header_output;
				return json_encode($json);
			} else{
				$json['Success'] = false;
				$json['Result'] = 'Problem with File uploading';
			}
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


}

?>