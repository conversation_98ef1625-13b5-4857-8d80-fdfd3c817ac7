<div class="row page" data-ng-controller="TruckBookingCalender">
    <style>
        .complete{ background-color: #b4dba4;}
        .noshow{ background-color: #f0908c;}
        .reserved{ background-color: #cecece;}
        .requested{ background-color: #a9c5ec;}
        .inprogress{ background-color: #ffe188;}
        .arrived{ background-color: #db96b5;}
        td,th{ text-align: center; line-height: 34px !important;}
    </style>
    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h">
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Truck Booking Calender</span>
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Date</label>
                                    <md-datepicker ng-model="Truck.ArrivalDate" required
                                        input-aria-describedby="datepicker-description"
                                        input-aria-labelledby="datepicker-header "></md-datepicker>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Type</label>
                                    <md-select name="ParkingType" ng-model="Truck.ParkingType" required>
                                        <md-option value="{{Parking.ParkingType}}"
                                            ng-repeat="Parking in ParkingTypes">{{Parking.ParkingTypeName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ParkingType.$error" multiple
                                            ng-if='material_signup_form.ParkingType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-button class="md-raised btn-w-md md-primary btn-w-md mt-10"
                                    data-ng-disabled="material_signup_form.$invalid" ng-click="TruckSave()">
                                    <span ng-show="! Truck.busy">Save</span>
                                    <span ng-show="Truck.busy"><md-progress-circular class="md-hue-2"
                                            md-mode="indeterminate" md-diameter="20px"
                                            style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row mt-10">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div class="table-container table-responsive" style="overflow: auto;">
                                <table class="table table-bordered">

                                    <thead>

                                        <tr class="bg-grey">
                                            <th style="min-width: 80px;">Time</th>
                                            <th>Dock_01</th>
                                            <th>Dock_02</th>
                                            <th>Dock_03</th>
                                            <th>Dock_04</th>
                                            <th>Yard_01</th>
                                            <th>Yard_02</th>
                                            <th>Yard_03</th>
                                            <th>Yard_04</th>
                                        </tr>

                                    </thead>

                                    <tbody>

                                        <tr>
                                            <td>7:30 am</td>
                                            <td class="complete">Complete</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td></td>
                                            <td></td>
                                        </tr>

                                        <tr>
                                            <td>8:00 am</td>
                                            <td class="complete">Complete</td>
                                            <td class="reserved">Reserved</td>
                                            <td></td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>

                                        <tr>
                                            <td>8:30 am</td>
                                            <td class="noshow">No Show</td>
                                            <td></td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td></td>
                                            <td></td>
                                        </tr>

                                        <tr>
                                            <td>9:00 am</td>
                                            <td class="requested">Requested</td>
                                            <td class="reserved">Reserved</td>
                                            <td class="complete">Complete</td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td></td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                        </tr>

                                        <tr>
                                            <td>9:30 am</td>
                                            <td class="reserved">Reserved</td>
                                            <td class="reserved">Reserved</td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td class="requested">Requested</td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td class="complete">Complete</td>
                                        </tr>

                                        <tr>
                                            <td>10:00 am</td>
                                            <td class="inprogress">In Progress</td>
                                            <td class="reserved">Reserved</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td></td>
                                            <td></td>
                                        </tr>

                                        <tr>
                                            <td>10:30 am</td>
                                            <td></td>
                                            <td class="requested">Requested</td>
                                            <td class="complete">Complete</td>
                                            <td class="requested">Requested</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td class="arrived">Arrived</td>
                                        </tr>

                                        <tr>
                                            <td>11:00 am</td>
                                            <td class="arrived">Arrived</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td></td>
                                            <td class="complete">Complete</td>
                                            <td class="reserved">Reserved</td>
                                        </tr>

                                        <tr>
                                            <td>11:30 am</td>
                                            <td class="reserved">Reserved</td>
                                            <td class="reserved">Reserved</td>
                                            <td class="arrived">Arrived</td>
                                            <td class="arrived">Arrived</td>
                                            <td class="reserved">Reserved</td>
                                            <td class="arrived">Arrived</td>
                                            <td></td>
                                            <td></td>
                                        </tr>

                                    </tbody>

                                    <tfoot>
                                    </tfoot>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </md-card>
        </article>
    </div>
</div>