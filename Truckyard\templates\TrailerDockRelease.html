<div class="row page" data-ng-controller="TrailerDockRelease" ng-cloak>

    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h">
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Trailer Dock/Release</span>
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Truck Reg</label>
                                    <input type="text" name="TruckReg"  ng-model="Truck['TruckReg']"  required ng-maxlength="15" />
                                     <div class="error-sapce">
                                        <div ng-messages="material_signup_form.TruckReg.$error"
                                            ng-if='material_signup_form.TruckReg.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Trailer Number</label>
                                    <input type="text" name="TrailerNumber"  ng-model="Truck['TrailerNumber']"  required ng-maxlength="50" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.TrailerNumber.$error" multiple ng-if='material_signup_form.TrailerNumber.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 10.</div>
                                        <div ng-message="maxlength">Max length 15.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>                            
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Location</label>
                                    <md-select name="ParkingLocation" ng-model="Truck.ParkingLocation" required>
                                        <md-option value="{{Parking.ParkingLocation}}"
                                            ng-repeat="Parking in ParkingLocations">{{Parking.ParkingLocationName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ParkingLocation.$error"
                                            ng-if='material_signup_form.ParkingLocation.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Carrier</label>
                                    <md-select name="Carrier" ng-model="Carrier.Carrier" required>
                                        <md-option value="{{Carrier.Carrier}}"
                                            ng-repeat="Carrier in Carriers">{{Carrier.CarrierName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.Carrier.$error" multiple
                                            ng-if='material_signup_form.Carrier.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div style="clear: both;"></div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Checkin Date</label>
                                    <md-datepicker ng-model="Truck.ArrivalDate" 
                                        input-aria-describedby="datepicker-description"
                                        input-aria-labelledby="datepicker-header "></md-datepicker>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Checkin Time</label>
                                    <input type="text" name="CheckinTime"  ng-model="Checkin['CheckinTime']" />
                                     <div class="error-sapce"></div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Vehicle Type</label>
                                    <md-select name="VehicleType" ng-model="Vehicle.VehicleType" required>
                                        <md-option value="{{Vehicle.VehicleType}}"
                                            ng-repeat="Vehicle in VehicleTypes">{{Vehicle.VehicleTypeName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.VehicleType.$error" multiple
                                            ng-if='material_signup_form.VehicleType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-button class="md-raised btn-w-md md-primary btn-w-md mt-10"
                                    data-ng-disabled="material_signup_form.$invalid" ng-click="TruckSave()">
                                    <span ng-show="! Truck.busy">GO</span>
                                    <span ng-show="Truck.busy"><md-progress-circular class="md-hue-2"
                                            md-mode="indeterminate" md-diameter="20px"
                                            style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>                       

                    </div>
                </div>

                <div class="row mt-10">
                    <div class="row">
                        <div class="col-md-5 col-md-offset-1">
                            <md-card>
                                <md-toolbar class="md-table-toolbar md-default" style="background-color: #f9f6ed;">
                                    <div class="md-toolbar-tools">
                                        <h3 style="text-align: cenetr;">Trailer Docking</h3>
                                    </div>
                                </md-toolbar>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="col-md-12">
                                            <h5><strong>Please Check:</strong></h5>
                                            <div>
                                                <md-checkbox ng-model="dock.opt1">
                                                    Go Outside with Spotter
                                                </md-checkbox>
                                            </div>
                                            <div>
                                                <md-checkbox ng-model="dock.opt2">
                                                    Check Landing Gear (Legs/Level) **
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="dock.opt3">
                                                    Place Gladlock **
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="dock.opt4">
                                                    Place 2 Trailer stands **
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="dock.opt5">
                                                    Verify/Place Wheel Chock **
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="dock.opt6">
                                                    Go Inside and Place Gladlock Key on Hook
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="dock.opt7">
                                                    Open Dock Door
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="dock.opt8">
                                                    Engage Dock Plate
                                                </md-checkbox>
                                            </div>
                                        </div>

                                        <div class="col-md-12 btns-row">
                                            <md-button class="md-raised btn-w-md md-warn btn-w-md"  ng-click="showAdvanced($event)">
                                                Unsafe to Proceed
                                            </md-button>
                                            <md-button class="md-raised btn-w-md md-primary btn-w-md"  ng-click="showAdvanced2($event)">
                                                Complete
                                            </md-button>
                                        </div>
                                    </div>
                                </div>
                            </md-card>
                        </div>

                        <div class="col-md-5">
                            <md-card>
                                <md-toolbar class="md-table-toolbar md-default" style="background-color: #d9e8fb;">
                                    <div class="md-toolbar-tools">
                                        <h3 style="text-align: cenetr;">Trailer Release</h3>
                                    </div>
                                </md-toolbar>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="col-md-12">
                                            <h5><strong>Please Check:</strong></h5>
                                            <div> 
                                                <md-checkbox ng-model="Release.opt1">
                                                    Remove Dock Plate
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="Release.opt2">
                                                    Close Dock Door
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="Release.opt3">
                                                    Go Outside with Spotter and Remove Trailer Stands **
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="Release.opt4">
                                                    Remove Glad Lock **
                                                </md-checkbox>
                                            </div>
                                            <div> 
                                                <md-checkbox ng-model="Release.opt5">
                                                    Go Inside and Disengage Dock Lock Override
                                                </md-checkbox>
                                            </div>
                                        </div>
                                        <div class="col-md-12 btns-row">
                                            <md-button class="md-raised btn-w-md md-primary btn-w-md"  ng-click="showAdvanced2($event)">
                                                Complete
                                            </md-button>
                                        </div>
                                    </div>
                                </div>
                            </md-card>
                        </div>

                    </div>
                </div>

            </md-card>
        </article>

    </div>
</div>


